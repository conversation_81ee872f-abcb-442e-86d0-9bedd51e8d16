"""
Model evaluation and comparison module
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Any
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import config
from utils import calculate_metrics, calculate_picp, calculate_mpiw, save_results

class ModelEvaluator:
    """Comprehensive model evaluation and comparison"""
    
    def __init__(self):
        self.results = {}
        self.predictions = {}
        self.uncertainty_bounds = {}
    
    def add_model_results(self, model_name: str, y_true: np.ndarray, y_pred: np.ndarray, 
                         uncertainty_bounds: Dict = None):
        """
        Add model results for evaluation
        
        Args:
            model_name: Name of the model
            y_true: True values
            y_pred: Predicted values
            uncertainty_bounds: Dictionary with uncertainty bounds for different confidence levels
        """
        # Store predictions
        self.predictions[model_name] = {
            'y_true': y_true,
            'y_pred': y_pred
        }
        
        # Store uncertainty bounds if provided
        if uncertainty_bounds:
            self.uncertainty_bounds[model_name] = uncertainty_bounds
        
        # Calculate basic metrics
        metrics = self.calculate_comprehensive_metrics(y_true, y_pred)
        
        # Calculate uncertainty metrics if bounds are available
        if uncertainty_bounds:
            uncertainty_metrics = self.calculate_uncertainty_metrics(y_true, uncertainty_bounds)
            metrics.update(uncertainty_metrics)
        
        self.results[model_name] = metrics
        
        print(f"=== {model_name} Model Results ===")
        for metric, value in metrics.items():
            print(f"{metric}: {value:.4f}")
    
    def calculate_comprehensive_metrics(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
        """Calculate comprehensive evaluation metrics"""
        # Align arrays in case of length mismatch
        min_length = min(len(y_true), len(y_pred))
        y_true = y_true[-min_length:]
        y_pred = y_pred[-min_length:]
        
        metrics = {}
        
        # Basic regression metrics
        metrics['MAE'] = mean_absolute_error(y_true, y_pred)
        metrics['RMSE'] = np.sqrt(mean_squared_error(y_true, y_pred))
        metrics['MAPE'] = np.mean(np.abs((y_true - y_pred) / y_true)) * 100
        metrics['R2'] = r2_score(y_true, y_pred)
        
        # Additional metrics
        metrics['Max_Error'] = np.max(np.abs(y_true - y_pred))
        metrics['Mean_Error'] = np.mean(y_true - y_pred)  # Bias
        metrics['Std_Error'] = np.std(y_true - y_pred)
        
        # Percentage metrics
        metrics['RMSE_Percentage'] = (metrics['RMSE'] / np.mean(y_true)) * 100
        metrics['MAE_Percentage'] = (metrics['MAE'] / np.mean(y_true)) * 100
        
        # Directional accuracy
        true_direction = np.diff(y_true) > 0
        pred_direction = np.diff(y_pred) > 0
        if len(true_direction) > 0:
            metrics['Directional_Accuracy'] = np.mean(true_direction == pred_direction) * 100
        else:
            metrics['Directional_Accuracy'] = 0
        
        return metrics
    
    def calculate_uncertainty_metrics(self, y_true: np.ndarray, uncertainty_bounds: Dict) -> Dict[str, float]:
        """Calculate uncertainty quantification metrics"""
        uncertainty_metrics = {}
        
        for confidence_key, bounds in uncertainty_bounds.items():
            if 'lower_bound' in bounds and 'upper_bound' in bounds:
                lower_bound = bounds['lower_bound']
                upper_bound = bounds['upper_bound']
                
                # Align arrays
                min_length = min(len(y_true), len(lower_bound), len(upper_bound))
                y_true_aligned = y_true[-min_length:]
                lower_aligned = lower_bound[-min_length:]
                upper_aligned = upper_bound[-min_length:]
                
                # Calculate PICP and MPIW
                picp = calculate_picp(y_true_aligned, lower_aligned, upper_aligned)
                mpiw = calculate_mpiw(lower_aligned, upper_aligned)
                
                # Normalize MPIW by mean of true values
                normalized_mpiw = mpiw / np.mean(y_true_aligned)
                
                uncertainty_metrics[f'PICP_{confidence_key}'] = picp
                uncertainty_metrics[f'MPIW_{confidence_key}'] = mpiw
                uncertainty_metrics[f'NMPIW_{confidence_key}'] = normalized_mpiw
        
        return uncertainty_metrics
    
    def compare_models(self) -> pd.DataFrame:
        """Compare all models and return results DataFrame"""
        if not self.results:
            raise ValueError("No model results available for comparison")
        
        # Create comparison DataFrame
        comparison_df = pd.DataFrame(self.results).T
        
        # Sort by RMSE (lower is better)
        comparison_df = comparison_df.sort_values('RMSE')
        
        print("=== MODEL COMPARISON ===")
        print(comparison_df.round(4))
        
        return comparison_df
    
    def plot_predictions_comparison(self, max_points: int = 200):
        """Plot predictions comparison for all models"""
        if not self.predictions:
            raise ValueError("No predictions available for plotting")

        n_models = len(self.predictions)
        fig, axes = plt.subplots(n_models, 1, figsize=(15, 5 * n_models))

        if n_models == 1:
            axes = [axes]

        for idx, (model_name, data) in enumerate(self.predictions.items()):
            y_true = data['y_true']
            y_pred = data['y_pred']

            # Convert to numpy arrays if they are pandas Series
            if hasattr(y_true, 'values'):
                y_true = y_true.values
            if hasattr(y_pred, 'values'):
                y_pred = y_pred.values

            # Limit points for better visualization
            if len(y_true) > max_points:
                indices = np.linspace(0, len(y_true) - 1, max_points, dtype=int)
                y_true = y_true[indices]
                y_pred = y_pred[indices]

            # Plot predictions
            x = range(len(y_true))
            axes[idx].plot(x, y_true, label='Actual', color='blue', alpha=0.7)
            axes[idx].plot(x, y_pred, label='Predicted', color='red', alpha=0.7)

            # Add uncertainty bounds if available
            if model_name in self.uncertainty_bounds:
                bounds_95 = self.uncertainty_bounds[model_name].get('confidence_95')
                if bounds_95:
                    lower = bounds_95['lower_bound']
                    upper = bounds_95['upper_bound']

                    # Convert to numpy arrays if they are pandas Series
                    if hasattr(lower, 'values'):
                        lower = lower.values
                    if hasattr(upper, 'values'):
                        upper = upper.values

                    if len(lower) > max_points:
                        lower = lower[indices]
                        upper = upper[indices]

                    axes[idx].fill_between(x, lower, upper, alpha=0.3, color='gray',
                                         label='95% Confidence Interval')

            axes[idx].set_title(f'{model_name} Predictions')
            axes[idx].set_xlabel('Time')
            axes[idx].set_ylabel('Demand')
            axes[idx].legend()
            axes[idx].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(f'{config.PLOTS_DIR}/predictions_comparison.png', dpi=config.DPI, bbox_inches='tight')
        plt.show()
    
    def plot_error_analysis(self):
        """Plot error analysis for all models"""
        if not self.predictions:
            raise ValueError("No predictions available for error analysis")

        n_models = len(self.predictions)
        fig, axes = plt.subplots(2, n_models, figsize=(5 * n_models, 10))

        if n_models == 1:
            axes = axes.reshape(2, 1)

        for idx, (model_name, data) in enumerate(self.predictions.items()):
            y_true = data['y_true']
            y_pred = data['y_pred']

            # Convert to numpy arrays if they are pandas Series
            if hasattr(y_true, 'values'):
                y_true = y_true.values
            if hasattr(y_pred, 'values'):
                y_pred = y_pred.values

            errors = y_true - y_pred
            
            # Error distribution
            axes[0, idx].hist(errors, bins=30, alpha=0.7, edgecolor='black')
            axes[0, idx].set_title(f'{model_name} - Error Distribution')
            axes[0, idx].set_xlabel('Error')
            axes[0, idx].set_ylabel('Frequency')
            axes[0, idx].grid(True, alpha=0.3)
            
            # Actual vs Predicted scatter plot
            axes[1, idx].scatter(y_true, y_pred, alpha=0.6)
            
            # Perfect prediction line
            min_val = min(y_true.min(), y_pred.min())
            max_val = max(y_true.max(), y_pred.max())
            axes[1, idx].plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8)
            
            axes[1, idx].set_title(f'{model_name} - Actual vs Predicted')
            axes[1, idx].set_xlabel('Actual')
            axes[1, idx].set_ylabel('Predicted')
            axes[1, idx].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(f'{config.PLOTS_DIR}/error_analysis.png', dpi=config.DPI, bbox_inches='tight')
        plt.show()
    
    def plot_metrics_comparison(self):
        """Plot metrics comparison bar chart"""
        if not self.results:
            raise ValueError("No results available for metrics comparison")
        
        # Select key metrics for comparison
        key_metrics = ['MAE', 'RMSE', 'MAPE', 'R2']
        
        # Create comparison data
        models = list(self.results.keys())
        metrics_data = {metric: [self.results[model].get(metric, 0) for model in models] 
                       for metric in key_metrics}
        
        # Create subplots
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        axes = axes.flatten()
        
        for idx, metric in enumerate(key_metrics):
            values = metrics_data[metric]
            bars = axes[idx].bar(models, values)
            axes[idx].set_title(f'{metric} Comparison')
            axes[idx].set_ylabel(metric)
            axes[idx].grid(True, alpha=0.3)
            
            # Add value labels on bars
            for bar, value in zip(bars, values):
                height = bar.get_height()
                axes[idx].text(bar.get_x() + bar.get_width()/2., height,
                             f'{value:.3f}', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig(f'{config.PLOTS_DIR}/metrics_comparison.png', dpi=config.DPI, bbox_inches='tight')
        plt.show()
    
    def plot_uncertainty_analysis(self):
        """Plot uncertainty analysis for models with uncertainty bounds"""
        models_with_uncertainty = [model for model in self.uncertainty_bounds.keys()]
        
        if not models_with_uncertainty:
            print("No models with uncertainty bounds available")
            return
        
        n_models = len(models_with_uncertainty)
        fig, axes = plt.subplots(1, n_models, figsize=(6 * n_models, 6))
        
        if n_models == 1:
            axes = [axes]
        
        for idx, model_name in enumerate(models_with_uncertainty):
            # Get PICP and MPIW for different confidence levels
            confidence_levels = []
            picp_values = []
            mpiw_values = []
            
            for key, value in self.results[model_name].items():
                if key.startswith('PICP_confidence_'):
                    conf_level = int(key.split('_')[-1]) / 100
                    confidence_levels.append(conf_level)
                    picp_values.append(value)
                    
                    # Get corresponding MPIW
                    mpiw_key = key.replace('PICP', 'NMPIW')
                    mpiw_values.append(self.results[model_name].get(mpiw_key, 0))
            
            if confidence_levels:
                # Plot PICP vs target coverage
                axes[idx].plot(confidence_levels, picp_values, 'o-', label='Actual PICP')
                axes[idx].plot(confidence_levels, confidence_levels, '--', label='Target Coverage')
                axes[idx].set_title(f'{model_name} - Uncertainty Calibration')
                axes[idx].set_xlabel('Target Coverage')
                axes[idx].set_ylabel('Actual PICP')
                axes[idx].legend()
                axes[idx].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(f'{config.PLOTS_DIR}/uncertainty_analysis.png', dpi=config.DPI, bbox_inches='tight')
        plt.show()
    
    def generate_evaluation_report(self) -> Dict[str, Any]:
        """Generate comprehensive evaluation report"""
        if not self.results:
            raise ValueError("No results available for report generation")
        
        report = {
            'model_comparison': self.compare_models().to_dict(),
            'best_model_by_metric': {},
            'summary_statistics': {}
        }
        
        # Find best model for each metric
        comparison_df = pd.DataFrame(self.results).T
        
        # Metrics where lower is better
        lower_better = ['MAE', 'RMSE', 'MAPE', 'Max_Error', 'RMSE_Percentage', 'MAE_Percentage']
        # Metrics where higher is better
        higher_better = ['R2', 'Directional_Accuracy']
        
        for metric in lower_better:
            if metric in comparison_df.columns:
                best_model = comparison_df[metric].idxmin()
                report['best_model_by_metric'][metric] = best_model
        
        for metric in higher_better:
            if metric in comparison_df.columns:
                best_model = comparison_df[metric].idxmax()
                report['best_model_by_metric'][metric] = best_model
        
        # Summary statistics
        for metric in comparison_df.columns:
            report['summary_statistics'][metric] = {
                'mean': comparison_df[metric].mean(),
                'std': comparison_df[metric].std(),
                'min': comparison_df[metric].min(),
                'max': comparison_df[metric].max()
            }
        
        # Save report
        save_results(report, 'evaluation_report.json')
        
        print("=== EVALUATION REPORT SUMMARY ===")
        print("Best models by metric:")
        for metric, model in report['best_model_by_metric'].items():
            print(f"  {metric}: {model}")
        
        return report
    
    def save_all_results(self):
        """Save all evaluation results"""
        # Save detailed results
        save_results(self.results, 'detailed_results.json')
        
        # Save predictions
        predictions_data = {}
        for model_name, data in self.predictions.items():
            predictions_data[model_name] = {
                'y_true': data['y_true'].tolist(),
                'y_pred': data['y_pred'].tolist()
            }
        save_results(predictions_data, 'predictions.json')
        
        # Save uncertainty bounds
        if self.uncertainty_bounds:
            uncertainty_data = {}
            for model_name, bounds in self.uncertainty_bounds.items():
                uncertainty_data[model_name] = {}
                for conf_key, conf_bounds in bounds.items():
                    uncertainty_data[model_name][conf_key] = {
                        'forecast': conf_bounds['forecast'].tolist(),
                        'lower_bound': conf_bounds['lower_bound'].tolist(),
                        'upper_bound': conf_bounds['upper_bound'].tolist()
                    }
            save_results(uncertainty_data, 'uncertainty_bounds.json')
        
        print("All results saved successfully!")

if __name__ == "__main__":
    # Example usage
    np.random.seed(42)
    
    # Generate sample data
    y_true = np.random.normal(100, 20, 1000)
    y_pred_arima = y_true + np.random.normal(0, 5, 1000)
    y_pred_lstm = y_true + np.random.normal(0, 3, 1000)
    
    # Create evaluator
    evaluator = ModelEvaluator()
    
    # Add model results
    evaluator.add_model_results('ARIMA', y_true, y_pred_arima)
    evaluator.add_model_results('LSTM', y_true, y_pred_lstm)
    
    # Generate plots and reports
    evaluator.plot_predictions_comparison()
    evaluator.plot_error_analysis()
    evaluator.plot_metrics_comparison()
    evaluator.generate_evaluation_report()
    evaluator.save_all_results()
