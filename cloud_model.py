"""
Cloud Model Theory implementation for uncertainty quantification
"""
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy import stats
from typing import <PERSON><PERSON>, Dict, List
import config
from utils import set_random_seed

class CloudModel:
    """
    Cloud Model implementation for uncertainty quantification
    
    Cloud Model uses three numerical characteristics:
    - Ex (Expectation): The expected value of the cloud
    - En (Entropy): The uncertainty measure of the qualitative concept
    - He (Hyper-entropy): The uncertainty measure of entropy
    """
    
    def __init__(self):
        self.Ex = None  # Expectation
        self.En = None  # Entropy
        self.He = None  # Hyper-entropy
        set_random_seed()
    
    def fit_from_data(self, data: np.ndarray) -> Tuple[float, float, float]:
        """
        Calculate cloud model parameters from historical data
        
        Args:
            data: Historical data array
            
        Returns:
            Tuple of (Ex, En, He)
        """
        # Calculate basic statistics
        mean = np.mean(data)
        std = np.std(data)
        
        # Calculate cloud model parameters
        self.Ex = mean
        
        # Entropy calculation using sample variance
        self.En = std
        
        # Hyper-entropy calculation (uncertainty of entropy)
        # Using coefficient of variation approach
        if len(data) > 1:
            # Calculate variance of local variances
            window_size = min(10, len(data) // 4)
            if window_size < 2:
                window_size = 2
            
            local_vars = []
            for i in range(len(data) - window_size + 1):
                window_data = data[i:i + window_size]
                local_vars.append(np.var(window_data))
            
            if len(local_vars) > 1:
                self.He = np.std(local_vars) / np.mean(local_vars) if np.mean(local_vars) > 0 else 0.1
            else:
                self.He = 0.1
        else:
            self.He = 0.1
        
        # Ensure He is reasonable
        self.He = max(0.01, min(self.He, self.En * 0.5))
        
        return self.Ex, self.En, self.He
    
    def fit_from_errors(self, errors: np.ndarray) -> Tuple[float, float, float]:
        """
        Calculate cloud model parameters from prediction errors
        
        Args:
            errors: Prediction errors array
            
        Returns:
            Tuple of (Ex, En, He)
        """
        # For errors, expectation should be close to zero for unbiased predictions
        self.Ex = np.mean(errors)
        
        # Entropy represents the spread of errors
        self.En = np.std(errors)
        
        # Hyper-entropy represents the uncertainty in error distribution
        # Calculate using quartile-based approach
        q75, q25 = np.percentile(errors, [75, 25])
        iqr = q75 - q25
        
        # Robust estimate of standard deviation
        robust_std = iqr / 1.349  # 1.349 is the IQR to std conversion factor for normal distribution
        
        # He represents the uncertainty in the uncertainty measure
        self.He = abs(self.En - robust_std) / max(self.En, robust_std, 0.001)
        
        # Ensure reasonable bounds
        self.He = max(0.01, min(self.He, self.En * 0.3))
        
        return self.Ex, self.En, self.He
    
    def generate_cloud_drops(self, n_samples: int = 1000) -> np.ndarray:
        """
        Generate cloud drops (samples) based on cloud model parameters
        
        Args:
            n_samples: Number of samples to generate
            
        Returns:
            Array of generated cloud drops
        """
        if self.Ex is None or self.En is None or self.He is None:
            raise ValueError("Cloud model parameters must be fitted first")
        
        # Generate cloud drops using normal distribution
        # En' ~ N(En, He^2)
        en_prime = np.random.normal(self.En, self.He, n_samples)
        
        # Ensure En' is positive
        en_prime = np.abs(en_prime)
        
        # x ~ N(Ex, (En')^2)
        cloud_drops = np.random.normal(self.Ex, en_prime)
        
        return cloud_drops
    
    def calculate_membership_degree(self, x: float) -> float:
        """
        Calculate membership degree for a given value
        
        Args:
            x: Input value
            
        Returns:
            Membership degree [0, 1]
        """
        if self.Ex is None or self.En is None or self.He is None:
            raise ValueError("Cloud model parameters must be fitted first")
        
        # Generate En' for this calculation
        en_prime = np.random.normal(self.En, self.He)
        en_prime = abs(en_prime)
        
        # Calculate membership degree
        if en_prime == 0:
            membership = 1.0 if x == self.Ex else 0.0
        else:
            membership = np.exp(-0.5 * ((x - self.Ex) / en_prime) ** 2)
        
        return membership
    
    def predict_interval(self, confidence_level: float = 0.95) -> Tuple[float, float]:
        """
        Calculate prediction interval based on cloud model
        
        Args:
            confidence_level: Confidence level for the interval
            
        Returns:
            Tuple of (lower_bound, upper_bound)
        """
        if self.Ex is None or self.En is None or self.He is None:
            raise ValueError("Cloud model parameters must be fitted first")
        
        # Calculate z-score for the confidence level
        alpha = 1 - confidence_level
        z_score = stats.norm.ppf(1 - alpha / 2)
        
        # Account for hyper-entropy in interval calculation
        effective_std = self.En * (1 + self.He)
        
        lower_bound = self.Ex - z_score * effective_std
        upper_bound = self.Ex + z_score * effective_std
        
        return lower_bound, upper_bound
    
    def get_parameters(self) -> Dict[str, float]:
        """Get cloud model parameters"""
        return {
            'Ex': self.Ex,
            'En': self.En,
            'He': self.He
        }
    
    def set_parameters(self, Ex: float, En: float, He: float):
        """Set cloud model parameters"""
        self.Ex = Ex
        self.En = En
        self.He = He

class UncertaintyQuantifier:
    """Uncertainty quantification using cloud model theory"""
    
    def __init__(self):
        self.cloud_models = {}
        self.historical_errors = {}
        set_random_seed()
    
    def fit_uncertainty_model(self, model_name: str, y_true: np.ndarray, y_pred: np.ndarray):
        """
        Fit uncertainty model for a specific prediction model
        
        Args:
            model_name: Name of the prediction model
            y_true: True values
            y_pred: Predicted values
        """
        # Calculate prediction errors
        errors = y_true - y_pred
        
        # Store historical errors
        self.historical_errors[model_name] = errors
        
        # Fit cloud model to errors
        cloud_model = CloudModel()
        cloud_model.fit_from_errors(errors)
        
        # Store cloud model
        self.cloud_models[model_name] = cloud_model
        
        print(f"=== Cloud Model Parameters for {model_name} ===")
        params = cloud_model.get_parameters()
        print(f"Ex (Expectation): {params['Ex']:.4f}")
        print(f"En (Entropy): {params['En']:.4f}")
        print(f"He (Hyper-entropy): {params['He']:.4f}")
        
        return cloud_model
    
    def predict_with_uncertainty(self, model_name: str, predictions: np.ndarray, 
                               confidence_levels: List[float] = None) -> Dict:
        """
        Generate uncertainty bounds for predictions
        
        Args:
            model_name: Name of the prediction model
            predictions: Point predictions
            confidence_levels: List of confidence levels
            
        Returns:
            Dictionary with uncertainty bounds
        """
        if confidence_levels is None:
            confidence_levels = config.CONFIDENCE_LEVELS
        
        if model_name not in self.cloud_models:
            raise ValueError(f"No uncertainty model fitted for {model_name}")
        
        cloud_model = self.cloud_models[model_name]
        results = {}
        
        for confidence_level in confidence_levels:
            # Calculate prediction intervals
            lower_offset, upper_offset = cloud_model.predict_interval(confidence_level)
            
            # Apply to predictions
            lower_bounds = predictions + lower_offset
            upper_bounds = predictions + upper_offset
            
            results[f'confidence_{int(confidence_level*100)}'] = {
                'forecast': predictions,
                'lower_bound': lower_bounds,
                'upper_bound': upper_bounds,
                'interval_width': upper_bounds - lower_bounds
            }
        
        return results
    
    def generate_uncertainty_samples(self, model_name: str, predictions: np.ndarray, 
                                   n_samples: int = 1000) -> np.ndarray:
        """
        Generate uncertainty samples using cloud model
        
        Args:
            model_name: Name of the prediction model
            predictions: Point predictions
            n_samples: Number of samples to generate
            
        Returns:
            Array of uncertainty samples
        """
        if model_name not in self.cloud_models:
            raise ValueError(f"No uncertainty model fitted for {model_name}")
        
        cloud_model = self.cloud_models[model_name]
        
        # Generate cloud drops for each prediction
        uncertainty_samples = []
        
        for pred in predictions:
            # Generate error samples
            error_samples = cloud_model.generate_cloud_drops(n_samples)
            
            # Add to prediction
            pred_samples = pred + error_samples
            uncertainty_samples.append(pred_samples)
        
        return np.array(uncertainty_samples)
    
    def plot_uncertainty_distribution(self, model_name: str):
        """Plot uncertainty distribution"""
        if model_name not in self.cloud_models:
            raise ValueError(f"No uncertainty model fitted for {model_name}")
        
        cloud_model = self.cloud_models[model_name]
        errors = self.historical_errors[model_name]
        
        # Generate cloud drops
        cloud_drops = cloud_model.generate_cloud_drops(10000)
        
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        # Plot historical errors vs cloud model
        axes[0].hist(errors, bins=50, alpha=0.7, label='Historical Errors', density=True)
        axes[0].hist(cloud_drops, bins=50, alpha=0.7, label='Cloud Model', density=True)
        axes[0].set_title(f'Error Distribution - {model_name}')
        axes[0].set_xlabel('Error')
        axes[0].set_ylabel('Density')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        # Plot cloud model parameters
        params = cloud_model.get_parameters()
        x = np.linspace(errors.min(), errors.max(), 1000)
        membership_degrees = [cloud_model.calculate_membership_degree(xi) for xi in x]
        
        axes[1].plot(x, membership_degrees, label='Membership Function')
        axes[1].axvline(params['Ex'], color='red', linestyle='--', label=f"Ex = {params['Ex']:.3f}")
        axes[1].axvline(params['Ex'] - params['En'], color='orange', linestyle='--', alpha=0.7, label=f"En = {params['En']:.3f}")
        axes[1].axvline(params['Ex'] + params['En'], color='orange', linestyle='--', alpha=0.7)
        axes[1].set_title(f'Cloud Model Membership Function - {model_name}')
        axes[1].set_xlabel('Error')
        axes[1].set_ylabel('Membership Degree')
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(f'{config.PLOTS_DIR}/uncertainty_distribution_{model_name}.png', 
                   dpi=config.DPI, bbox_inches='tight')
        plt.show()
    
    def compare_uncertainty_models(self, model_names: List[str]):
        """Compare uncertainty models across different prediction models"""
        if not all(name in self.cloud_models for name in model_names):
            missing = [name for name in model_names if name not in self.cloud_models]
            raise ValueError(f"Missing uncertainty models for: {missing}")
        
        # Create comparison plot
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        # Plot cloud model parameters
        ex_values = [self.cloud_models[name].Ex for name in model_names]
        en_values = [self.cloud_models[name].En for name in model_names]
        he_values = [self.cloud_models[name].He for name in model_names]
        
        x_pos = np.arange(len(model_names))
        width = 0.25
        
        axes[0].bar(x_pos - width, ex_values, width, label='Ex (Expectation)')
        axes[0].bar(x_pos, en_values, width, label='En (Entropy)')
        axes[0].bar(x_pos + width, he_values, width, label='He (Hyper-entropy)')
        axes[0].set_title('Cloud Model Parameters Comparison')
        axes[0].set_xlabel('Models')
        axes[0].set_ylabel('Parameter Value')
        axes[0].set_xticks(x_pos)
        axes[0].set_xticklabels(model_names)
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        # Plot error distributions
        for name in model_names:
            errors = self.historical_errors[name]
            axes[1].hist(errors, bins=30, alpha=0.6, label=name, density=True)
        
        axes[1].set_title('Error Distributions Comparison')
        axes[1].set_xlabel('Error')
        axes[1].set_ylabel('Density')
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(f'{config.PLOTS_DIR}/uncertainty_models_comparison.png', 
                   dpi=config.DPI, bbox_inches='tight')
        plt.show()

if __name__ == "__main__":
    # Example usage
    np.random.seed(42)
    
    # Generate sample data
    y_true = np.random.normal(100, 20, 1000)
    y_pred_arima = y_true + np.random.normal(0, 5, 1000)
    y_pred_lstm = y_true + np.random.normal(0, 3, 1000)
    
    # Initialize uncertainty quantifier
    uq = UncertaintyQuantifier()
    
    # Fit uncertainty models
    uq.fit_uncertainty_model('ARIMA', y_true, y_pred_arima)
    uq.fit_uncertainty_model('LSTM', y_true, y_pred_lstm)
    
    # Generate uncertainty bounds
    arima_uncertainty = uq.predict_with_uncertainty('ARIMA', y_pred_arima[:100])
    lstm_uncertainty = uq.predict_with_uncertainty('LSTM', y_pred_lstm[:100])
    
    # Plot uncertainty distributions
    uq.plot_uncertainty_distribution('ARIMA')
    uq.plot_uncertainty_distribution('LSTM')
    
    # Compare models
    uq.compare_uncertainty_models(['ARIMA', 'LSTM'])
