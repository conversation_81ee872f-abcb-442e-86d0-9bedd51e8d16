"""
Utility functions for bike sharing demand prediction system
"""
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Tuple, List, Dict, Any
import config

def set_random_seed(seed: int = config.RANDOM_SEED):
    """Set random seed for reproducibility"""
    np.random.seed(seed)
    import tensorflow as tf
    tf.random.set_seed(seed)

def create_sequences(data: np.ndarray, sequence_length: int) -> Tuple[np.ndarray, np.ndarray]:
    """
    Create sequences for LSTM training
    
    Args:
        data: Input time series data
        sequence_length: Length of input sequences
        
    Returns:
        X: Input sequences
        y: Target values
    """
    X, y = [], []
    for i in range(len(data) - sequence_length):
        X.append(data[i:(i + sequence_length)])
        y.append(data[i + sequence_length])
    return np.array(X), np.array(y)

def calculate_metrics(y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
    """
    Calculate evaluation metrics
    
    Args:
        y_true: True values
        y_pred: Predicted values
        
    Returns:
        Dictionary of metrics
    """
    mae = np.mean(np.abs(y_true - y_pred))
    rmse = np.sqrt(np.mean((y_true - y_pred) ** 2))
    mape = np.mean(np.abs((y_true - y_pred) / y_true)) * 100
    
    return {
        'MAE': mae,
        'RMSE': rmse,
        'MAPE': mape
    }

def calculate_picp(y_true: np.ndarray, lower_bound: np.ndarray, upper_bound: np.ndarray) -> float:
    """
    Calculate Prediction Interval Coverage Probability (PICP)
    
    Args:
        y_true: True values
        lower_bound: Lower prediction bounds
        upper_bound: Upper prediction bounds
        
    Returns:
        PICP value
    """
    coverage = np.mean((y_true >= lower_bound) & (y_true <= upper_bound))
    return coverage

def calculate_mpiw(lower_bound: np.ndarray, upper_bound: np.ndarray) -> float:
    """
    Calculate Mean Prediction Interval Width (MPIW)
    
    Args:
        lower_bound: Lower prediction bounds
        upper_bound: Upper prediction bounds
        
    Returns:
        MPIW value
    """
    return np.mean(upper_bound - lower_bound)

def plot_time_series(data: pd.DataFrame, columns: List[str], title: str = "Time Series Plot"):
    """
    Plot time series data
    
    Args:
        data: DataFrame with datetime index
        columns: List of columns to plot
        title: Plot title
    """
    plt.figure(figsize=config.FIGURE_SIZE)
    for col in columns:
        plt.plot(data.index, data[col], label=col)
    plt.title(title)
    plt.xlabel('Time')
    plt.ylabel('Value')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()

def plot_predictions_with_uncertainty(y_true: np.ndarray, y_pred: np.ndarray, 
                                    lower_bound: np.ndarray, upper_bound: np.ndarray,
                                    title: str = "Predictions with Uncertainty"):
    """
    Plot predictions with uncertainty bounds
    
    Args:
        y_true: True values
        y_pred: Predicted values
        lower_bound: Lower prediction bounds
        upper_bound: Upper prediction bounds
        title: Plot title
    """
    plt.figure(figsize=config.FIGURE_SIZE)
    x = range(len(y_true))
    
    plt.plot(x, y_true, label='True', color='blue', alpha=0.7)
    plt.plot(x, y_pred, label='Predicted', color='red', alpha=0.7)
    plt.fill_between(x, lower_bound, upper_bound, alpha=0.3, color='gray', label='Uncertainty')
    
    plt.title(title)
    plt.xlabel('Time')
    plt.ylabel('Demand')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()

def save_results(results: Dict[str, Any], filename: str):
    """
    Save results to file
    
    Args:
        results: Results dictionary
        filename: Output filename
    """
    import json
    import os
    
    filepath = os.path.join(config.RESULTS_DIR, filename)
    with open(filepath, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    print(f"Results saved to {filepath}")

def load_results(filename: str) -> Dict[str, Any]:
    """
    Load results from file
    
    Args:
        filename: Input filename
        
    Returns:
        Results dictionary
    """
    import json
    import os
    
    filepath = os.path.join(config.RESULTS_DIR, filename)
    with open(filepath, 'r') as f:
        results = json.load(f)
    return results
