"""
Decision Support System for bike sharing operations with uncertainty quantification
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Any
from scipy.optimize import minimize
import config
from utils import save_results

class BikeRebalancingSimulator:
    """Simulate bike rebalancing strategies with uncertainty"""
    
    def __init__(self, n_stations: int = 10, bike_capacity: int = 20):
        self.n_stations = n_stations
        self.bike_capacity = bike_capacity
        self.stations = {}
        self.rebalancing_cost = 2.0  # Cost per bike moved
        self.shortage_penalty = 10.0  # Penalty per unmet demand
        self.excess_penalty = 1.0    # Penalty per excess bike
        
        # Initialize stations
        for i in range(n_stations):
            self.stations[i] = {
                'current_bikes': bike_capacity // 2,
                'capacity': bike_capacity,
                'demand_history': [],
                'supply_history': []
            }
    
    def simulate_demand_supply(self, predictions: Dict, uncertainty_bounds: Dict = None, 
                             time_horizon: int = 24) -> Dict:
        """
        Simulate demand and supply with uncertainty
        
        Args:
            predictions: Dictionary with demand predictions for each station
            uncertainty_bounds: Uncertainty bounds for predictions
            time_horizon: Number of time steps to simulate
            
        Returns:
            Simulation results
        """
        results = {
            'station_states': [],
            'shortages': [],
            'excesses': [],
            'rebalancing_actions': [],
            'costs': [],
            'satisfaction_rates': []
        }
        
        for t in range(time_horizon):
            station_state = {}
            total_shortage = 0
            total_excess = 0
            total_demand = 0
            total_satisfied = 0
            
            for station_id in range(self.n_stations):
                # Get predicted demand (with uncertainty if available)
                if uncertainty_bounds and f'station_{station_id}' in uncertainty_bounds:
                    # Sample from uncertainty distribution
                    bounds = uncertainty_bounds[f'station_{station_id}']['confidence_95']
                    lower = bounds['lower_bound'][t] if t < len(bounds['lower_bound']) else 0
                    upper = bounds['upper_bound'][t] if t < len(bounds['upper_bound']) else 0
                    demand = np.random.uniform(max(0, lower), max(0, upper))
                else:
                    # Use point prediction
                    pred_key = f'station_{station_id}'
                    if pred_key in predictions and t < len(predictions[pred_key]):
                        demand = max(0, predictions[pred_key][t])
                    else:
                        demand = np.random.poisson(5)  # Default demand
                
                # Current bikes at station
                current_bikes = self.stations[station_id]['current_bikes']
                
                # Calculate shortage and excess
                shortage = max(0, demand - current_bikes)
                satisfied_demand = min(demand, current_bikes)
                remaining_bikes = max(0, current_bikes - demand)
                excess = max(0, remaining_bikes - self.bike_capacity * 0.8)  # 80% capacity threshold
                
                # Update station state
                self.stations[station_id]['current_bikes'] = remaining_bikes
                self.stations[station_id]['demand_history'].append(demand)
                
                station_state[station_id] = {
                    'demand': demand,
                    'current_bikes': remaining_bikes,
                    'shortage': shortage,
                    'excess': excess,
                    'satisfied_demand': satisfied_demand
                }
                
                total_shortage += shortage
                total_excess += excess
                total_demand += demand
                total_satisfied += satisfied_demand
            
            # Calculate satisfaction rate
            satisfaction_rate = total_satisfied / total_demand if total_demand > 0 else 1.0
            
            results['station_states'].append(station_state)
            results['shortages'].append(total_shortage)
            results['excesses'].append(total_excess)
            results['satisfaction_rates'].append(satisfaction_rate)
        
        return results
    
    def optimize_rebalancing(self, current_state: Dict, predicted_demand: Dict, 
                           uncertainty_bounds: Dict = None) -> Dict:
        """
        Optimize bike rebalancing decisions considering uncertainty
        
        Args:
            current_state: Current state of all stations
            predicted_demand: Predicted demand for next period
            uncertainty_bounds: Uncertainty bounds for predictions
            
        Returns:
            Optimal rebalancing decisions
        """
        n_stations = len(current_state)
        
        # Define optimization variables: bikes to move from station i to station j
        # x[i*n_stations + j] represents bikes moved from station i to station j
        
        def objective(x):
            """Objective function: minimize total cost including uncertainty penalty"""
            x = x.reshape(n_stations, n_stations)
            total_cost = 0
            
            # Rebalancing cost
            rebalancing_cost = np.sum(x) * self.rebalancing_cost
            
            # Expected shortage and excess costs
            for i in range(n_stations):
                # Calculate expected bikes after rebalancing
                bikes_out = np.sum(x[i, :])  # Bikes moved out
                bikes_in = np.sum(x[:, i])   # Bikes moved in
                expected_bikes = current_state[i]['current_bikes'] - bikes_out + bikes_in
                
                # Expected demand (use mean if uncertainty bounds available)
                if uncertainty_bounds and f'station_{i}' in uncertainty_bounds:
                    bounds = uncertainty_bounds[f'station_{i}']['confidence_95']
                    expected_demand = (bounds['lower_bound'][0] + bounds['upper_bound'][0]) / 2
                    demand_std = (bounds['upper_bound'][0] - bounds['lower_bound'][0]) / 4  # Approximate std
                else:
                    expected_demand = predicted_demand.get(f'station_{i}', [0])[0]
                    demand_std = expected_demand * 0.2  # Assume 20% coefficient of variation
                
                # Expected shortage cost
                expected_shortage = max(0, expected_demand - expected_bikes)
                shortage_cost = expected_shortage * self.shortage_penalty
                
                # Uncertainty penalty (higher penalty for higher uncertainty)
                uncertainty_penalty = demand_std * 0.5
                
                total_cost += shortage_cost + uncertainty_penalty
            
            return total_cost + rebalancing_cost
        
        # Constraints
        constraints = []
        
        # Station capacity constraints
        for i in range(n_stations):
            def capacity_constraint(x, station=i):
                x = x.reshape(n_stations, n_stations)
                bikes_out = np.sum(x[station, :])
                bikes_in = np.sum(x[:, station])
                final_bikes = current_state[station]['current_bikes'] - bikes_out + bikes_in
                return self.bike_capacity - final_bikes
            
            constraints.append({'type': 'ineq', 'fun': capacity_constraint})
        
        # Non-negativity and availability constraints
        bounds = []
        for i in range(n_stations):
            for j in range(n_stations):
                if i == j:
                    bounds.append((0, 0))  # Can't move bikes to same station
                else:
                    max_move = current_state[i]['current_bikes']
                    bounds.append((0, max_move))
        
        # Initial guess
        x0 = np.zeros(n_stations * n_stations)
        
        # Optimize
        result = minimize(objective, x0, method='SLSQP', bounds=bounds, constraints=constraints)
        
        # Parse results
        optimal_moves = result.x.reshape(n_stations, n_stations)
        
        rebalancing_plan = {}
        for i in range(n_stations):
            for j in range(n_stations):
                if optimal_moves[i, j] > 0.1:  # Only include significant moves
                    rebalancing_plan[f'{i}_to_{j}'] = int(optimal_moves[i, j])
        
        return {
            'rebalancing_plan': rebalancing_plan,
            'total_cost': result.fun,
            'optimization_success': result.success
        }

class DecisionSupportSystem:
    """Comprehensive decision support system"""
    
    def __init__(self):
        self.simulator = BikeRebalancingSimulator()
        self.strategies = {}
        self.performance_metrics = {}
    
    def evaluate_strategy(self, strategy_name: str, predictions: Dict, 
                         uncertainty_bounds: Dict = None, n_simulations: int = 100) -> Dict:
        """
        Evaluate a rebalancing strategy through Monte Carlo simulation
        
        Args:
            strategy_name: Name of the strategy
            predictions: Demand predictions
            uncertainty_bounds: Uncertainty bounds
            n_simulations: Number of simulation runs
            
        Returns:
            Strategy performance metrics
        """
        print(f"=== Evaluating Strategy: {strategy_name} ===")
        
        simulation_results = []
        
        for sim in range(n_simulations):
            # Reset simulator
            self.simulator = BikeRebalancingSimulator()
            
            # Run simulation
            results = self.simulator.simulate_demand_supply(
                predictions, uncertainty_bounds, time_horizon=24
            )
            
            simulation_results.append(results)
        
        # Aggregate results
        performance = self.aggregate_simulation_results(simulation_results)
        self.performance_metrics[strategy_name] = performance
        
        print(f"Average Satisfaction Rate: {performance['avg_satisfaction_rate']:.3f}")
        print(f"Average Daily Shortage: {performance['avg_daily_shortage']:.1f}")
        print(f"Average Daily Excess: {performance['avg_daily_excess']:.1f}")
        
        return performance
    
    def aggregate_simulation_results(self, simulation_results: List[Dict]) -> Dict:
        """Aggregate results from multiple simulation runs"""
        aggregated = {
            'satisfaction_rates': [],
            'daily_shortages': [],
            'daily_excesses': [],
            'total_costs': []
        }
        
        for results in simulation_results:
            aggregated['satisfaction_rates'].extend(results['satisfaction_rates'])
            aggregated['daily_shortages'].append(sum(results['shortages']))
            aggregated['daily_excesses'].append(sum(results['excesses']))
        
        # Calculate summary statistics
        performance = {
            'avg_satisfaction_rate': np.mean(aggregated['satisfaction_rates']),
            'std_satisfaction_rate': np.std(aggregated['satisfaction_rates']),
            'min_satisfaction_rate': np.min(aggregated['satisfaction_rates']),
            'avg_daily_shortage': np.mean(aggregated['daily_shortages']),
            'std_daily_shortage': np.std(aggregated['daily_shortages']),
            'avg_daily_excess': np.mean(aggregated['daily_excesses']),
            'std_daily_excess': np.std(aggregated['daily_excesses']),
            'satisfaction_rate_95th': np.percentile(aggregated['satisfaction_rates'], 95),
            'satisfaction_rate_5th': np.percentile(aggregated['satisfaction_rates'], 5)
        }
        
        return performance
    
    def compare_strategies(self, strategies: Dict[str, Dict]) -> pd.DataFrame:
        """
        Compare multiple strategies
        
        Args:
            strategies: Dictionary of strategy configurations
            
        Returns:
            Comparison DataFrame
        """
        comparison_results = {}
        
        for strategy_name, config in strategies.items():
            performance = self.evaluate_strategy(
                strategy_name,
                config['predictions'],
                config.get('uncertainty_bounds'),
                config.get('n_simulations', 50)
            )
            comparison_results[strategy_name] = performance
        
        # Create comparison DataFrame
        comparison_df = pd.DataFrame(comparison_results).T
        comparison_df = comparison_df.sort_values('avg_satisfaction_rate', ascending=False)
        
        print("=== STRATEGY COMPARISON ===")
        print(comparison_df.round(3))
        
        return comparison_df
    
    def plot_strategy_comparison(self):
        """Plot strategy comparison results"""
        if not self.performance_metrics:
            print("No strategy results available for plotting")
            return
        
        strategies = list(self.performance_metrics.keys())
        metrics = ['avg_satisfaction_rate', 'avg_daily_shortage', 'avg_daily_excess']
        
        fig, axes = plt.subplots(1, 3, figsize=(18, 6))
        
        for idx, metric in enumerate(metrics):
            values = [self.performance_metrics[strategy][metric] for strategy in strategies]
            bars = axes[idx].bar(strategies, values)
            
            # Add value labels
            for bar, value in zip(bars, values):
                height = bar.get_height()
                axes[idx].text(bar.get_x() + bar.get_width()/2., height,
                             f'{value:.3f}', ha='center', va='bottom')
            
            axes[idx].set_title(metric.replace('_', ' ').title())
            axes[idx].set_ylabel('Value')
            axes[idx].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(f'{config.PLOTS_DIR}/strategy_comparison.png', dpi=config.DPI, bbox_inches='tight')
        plt.show()
    
    def analyze_uncertainty_impact(self, base_predictions: Dict, 
                                 uncertainty_levels: List[float] = [0.1, 0.2, 0.3, 0.4, 0.5]) -> Dict:
        """
        Analyze the impact of different uncertainty levels on performance
        
        Args:
            base_predictions: Base demand predictions
            uncertainty_levels: List of uncertainty levels to test
            
        Returns:
            Impact analysis results
        """
        print("=== Analyzing Uncertainty Impact ===")
        
        impact_results = {}
        
        for uncertainty_level in uncertainty_levels:
            # Create artificial uncertainty bounds
            uncertainty_bounds = {}
            for station_key, predictions in base_predictions.items():
                pred_array = np.array(predictions)
                uncertainty = pred_array * uncertainty_level
                
                uncertainty_bounds[station_key] = {
                    'confidence_95': {
                        'lower_bound': pred_array - 1.96 * uncertainty,
                        'upper_bound': pred_array + 1.96 * uncertainty
                    }
                }
            
            # Evaluate strategy with this uncertainty level
            strategy_name = f'uncertainty_{uncertainty_level:.1f}'
            performance = self.evaluate_strategy(
                strategy_name, base_predictions, uncertainty_bounds, n_simulations=30
            )
            
            impact_results[uncertainty_level] = performance
        
        # Plot impact analysis
        self.plot_uncertainty_impact(impact_results)
        
        return impact_results
    
    def plot_uncertainty_impact(self, impact_results: Dict):
        """Plot uncertainty impact analysis"""
        uncertainty_levels = list(impact_results.keys())
        satisfaction_rates = [impact_results[level]['avg_satisfaction_rate'] for level in uncertainty_levels]
        shortages = [impact_results[level]['avg_daily_shortage'] for level in uncertainty_levels]
        
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        # Satisfaction rate vs uncertainty
        axes[0].plot(uncertainty_levels, satisfaction_rates, 'o-', linewidth=2, markersize=8)
        axes[0].set_title('Satisfaction Rate vs Uncertainty Level')
        axes[0].set_xlabel('Uncertainty Level')
        axes[0].set_ylabel('Average Satisfaction Rate')
        axes[0].grid(True, alpha=0.3)
        
        # Shortage vs uncertainty
        axes[1].plot(uncertainty_levels, shortages, 'o-', linewidth=2, markersize=8, color='red')
        axes[1].set_title('Daily Shortage vs Uncertainty Level')
        axes[1].set_xlabel('Uncertainty Level')
        axes[1].set_ylabel('Average Daily Shortage')
        axes[1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(f'{config.PLOTS_DIR}/uncertainty_impact.png', dpi=config.DPI, bbox_inches='tight')
        plt.show()
    
    def generate_recommendations(self) -> Dict[str, Any]:
        """Generate operational recommendations based on analysis"""
        if not self.performance_metrics:
            return {"error": "No performance metrics available"}
        
        # Find best strategy
        best_strategy = max(self.performance_metrics.keys(), 
                          key=lambda x: self.performance_metrics[x]['avg_satisfaction_rate'])
        
        best_performance = self.performance_metrics[best_strategy]
        
        recommendations = {
            'best_strategy': best_strategy,
            'expected_satisfaction_rate': best_performance['avg_satisfaction_rate'],
            'risk_assessment': {
                'worst_case_satisfaction': best_performance['satisfaction_rate_5th'],
                'best_case_satisfaction': best_performance['satisfaction_rate_95th'],
                'satisfaction_volatility': best_performance['std_satisfaction_rate']
            },
            'operational_insights': [],
            'improvement_suggestions': []
        }
        
        # Generate insights
        if best_performance['avg_satisfaction_rate'] > 0.9:
            recommendations['operational_insights'].append("High satisfaction rate achieved")
        elif best_performance['avg_satisfaction_rate'] > 0.8:
            recommendations['operational_insights'].append("Good satisfaction rate with room for improvement")
        else:
            recommendations['operational_insights'].append("Low satisfaction rate - significant improvements needed")
        
        if best_performance['std_satisfaction_rate'] > 0.1:
            recommendations['improvement_suggestions'].append("High variability in satisfaction - consider more robust rebalancing")
        
        if best_performance['avg_daily_shortage'] > 10:
            recommendations['improvement_suggestions'].append("High shortage levels - increase bike availability")
        
        # Save recommendations
        save_results(recommendations, 'operational_recommendations.json')
        
        print("=== OPERATIONAL RECOMMENDATIONS ===")
        print(f"Best Strategy: {best_strategy}")
        print(f"Expected Satisfaction Rate: {best_performance['avg_satisfaction_rate']:.3f}")
        print(f"Risk Range: {best_performance['satisfaction_rate_5th']:.3f} - {best_performance['satisfaction_rate_95th']:.3f}")
        
        return recommendations

if __name__ == "__main__":
    # Example usage
    np.random.seed(42)
    
    # Create sample predictions for 10 stations
    base_predictions = {}
    for i in range(10):
        # Generate realistic demand patterns
        hourly_pattern = np.sin(np.linspace(0, 2*np.pi, 24)) * 5 + 10 + np.random.normal(0, 1, 24)
        base_predictions[f'station_{i}'] = np.maximum(0, hourly_pattern)
    
    # Initialize decision support system
    dss = DecisionSupportSystem()
    
    # Define strategies to compare
    strategies = {
        'deterministic': {
            'predictions': base_predictions,
            'n_simulations': 50
        },
        'with_uncertainty': {
            'predictions': base_predictions,
            'uncertainty_bounds': {},  # Will be generated
            'n_simulations': 50
        }
    }
    
    # Add uncertainty bounds for second strategy
    for station_key, predictions in base_predictions.items():
        pred_array = np.array(predictions)
        uncertainty = pred_array * 0.2  # 20% uncertainty
        
        strategies['with_uncertainty']['uncertainty_bounds'][station_key] = {
            'confidence_95': {
                'lower_bound': pred_array - 1.96 * uncertainty,
                'upper_bound': pred_array + 1.96 * uncertainty
            }
        }
    
    # Compare strategies
    comparison_df = dss.compare_strategies(strategies)
    
    # Plot comparison
    dss.plot_strategy_comparison()
    
    # Analyze uncertainty impact
    impact_results = dss.analyze_uncertainty_impact(base_predictions)
    
    # Generate recommendations
    recommendations = dss.generate_recommendations()
