"""
Data processing module for bike sharing demand prediction
"""
import pandas as pd
import numpy as np
import requests
import zipfile
import os
from typing import Tuple
import config
from utils import set_random_seed

class DataProcessor:
    """Data processing class for bike sharing dataset"""
    
    def __init__(self):
        self.data = None
        self.processed_data = None
        set_random_seed()
    
    def download_data(self) -> None:
        """Download UCI Bike Sharing dataset"""
        if not os.path.exists(os.path.join(config.DATA_DIR, config.RAW_DATA_FILE)):
            print("Downloading UCI Bike Sharing dataset...")
            
            # Download the zip file
            response = requests.get(config.DATA_URL)
            zip_path = os.path.join(config.DATA_DIR, "bike_sharing.zip")
            
            with open(zip_path, 'wb') as f:
                f.write(response.content)
            
            # Extract the zip file
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(config.DATA_DIR)
            
            # Remove the zip file
            os.remove(zip_path)
            print("Dataset downloaded and extracted successfully!")
        else:
            print("Dataset already exists!")
    
    def load_data(self) -> pd.DataFrame:
        """Load the bike sharing dataset"""
        file_path = os.path.join(config.DATA_DIR, config.RAW_DATA_FILE)
        
        if not os.path.exists(file_path):
            self.download_data()
        
        self.data = pd.read_csv(file_path)
        print(f"Data loaded successfully! Shape: {self.data.shape}")
        return self.data
    
    def clean_data(self) -> pd.DataFrame:
        """Clean the dataset"""
        if self.data is None:
            self.load_data()
        
        # Create a copy for processing
        df = self.data.copy()
        
        # Convert datetime
        df['dteday'] = pd.to_datetime(df['dteday'])
        df['datetime'] = df['dteday'] + pd.to_timedelta(df['hr'], unit='h')
        df.set_index('datetime', inplace=True)
        
        # Check for missing values
        missing_values = df.isnull().sum()
        if missing_values.sum() > 0:
            print("Missing values found:")
            print(missing_values[missing_values > 0])
            # Fill missing values with forward fill
            df.fillna(method='ffill', inplace=True)
        
        # Remove outliers using IQR method for demand (cnt)
        Q1 = df['cnt'].quantile(0.25)
        Q3 = df['cnt'].quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        
        outliers_mask = (df['cnt'] < lower_bound) | (df['cnt'] > upper_bound)
        print(f"Removing {outliers_mask.sum()} outliers ({outliers_mask.mean()*100:.2f}% of data)")
        
        df = df[~outliers_mask]
        
        self.data = df
        print("Data cleaning completed!")
        return df
    
    def engineer_features(self) -> pd.DataFrame:
        """Engineer additional features"""
        if self.data is None:
            self.clean_data()
        
        df = self.data.copy()
        
        # Time-based features
        df['hour'] = df.index.hour
        df['day'] = df.index.day
        df['month'] = df.index.month
        df['year'] = df.index.year
        df['weekday'] = df.index.weekday
        df['is_weekend'] = (df['weekday'] >= 5).astype(int)
        
        # Cyclical encoding for time features
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        df['weekday_sin'] = np.sin(2 * np.pi * df['weekday'] / 7)
        df['weekday_cos'] = np.cos(2 * np.pi * df['weekday'] / 7)
        
        # Weather interaction features
        df['temp_hum'] = df['temp'] * df['hum']
        df['temp_windspeed'] = df['temp'] * df['windspeed']
        df['feels_like_temp'] = df['atemp']
        
        # Rush hour indicators
        df['is_rush_hour'] = ((df['hour'].isin([7, 8, 9, 17, 18, 19])) & 
                             (df['workingday'] == 1)).astype(int)
        
        # Weather severity
        df['weather_severity'] = df['weathersit']
        
        # Lag features
        for lag in [1, 2, 3, 24, 168]:  # 1h, 2h, 3h, 1day, 1week
            df[f'cnt_lag_{lag}'] = df['cnt'].shift(lag)
        
        # Rolling statistics
        for window in [3, 6, 12, 24]:
            df[f'cnt_rolling_mean_{window}'] = df['cnt'].rolling(window=window).mean()
            df[f'cnt_rolling_std_{window}'] = df['cnt'].rolling(window=window).std()
        
        # Drop rows with NaN values created by lag and rolling features
        df.dropna(inplace=True)
        
        self.processed_data = df
        print(f"Feature engineering completed! New shape: {df.shape}")
        return df
    
    def split_data(self) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """Split data into train, validation, and test sets"""
        if self.processed_data is None:
            self.engineer_features()
        
        df = self.processed_data.copy()
        
        # Calculate split indices
        n = len(df)
        train_end = int(n * config.TRAIN_RATIO)
        val_end = int(n * (config.TRAIN_RATIO + config.VALIDATION_RATIO))
        
        # Split the data
        train_data = df.iloc[:train_end]
        val_data = df.iloc[train_end:val_end]
        test_data = df.iloc[val_end:]
        
        print(f"Data split completed!")
        print(f"Train: {len(train_data)} samples ({len(train_data)/n*100:.1f}%)")
        print(f"Validation: {len(val_data)} samples ({len(val_data)/n*100:.1f}%)")
        print(f"Test: {len(test_data)} samples ({len(test_data)/n*100:.1f}%)")
        
        return train_data, val_data, test_data
    
    def save_processed_data(self) -> None:
        """Save processed data to file"""
        if self.processed_data is None:
            self.engineer_features()
        
        file_path = os.path.join(config.DATA_DIR, config.PROCESSED_DATA_FILE)
        self.processed_data.to_csv(file_path)
        print(f"Processed data saved to {file_path}")
    
    def load_processed_data(self) -> pd.DataFrame:
        """Load processed data from file"""
        file_path = os.path.join(config.DATA_DIR, config.PROCESSED_DATA_FILE)
        
        if os.path.exists(file_path):
            self.processed_data = pd.read_csv(file_path, index_col=0, parse_dates=True)
            print(f"Processed data loaded from {file_path}")
            return self.processed_data
        else:
            print("Processed data file not found. Processing raw data...")
            return self.engineer_features()
    
    def get_feature_columns(self) -> dict:
        """Get feature column names by category"""
        if self.processed_data is None:
            self.load_processed_data()
        
        all_columns = self.processed_data.columns.tolist()
        
        # Define feature categories
        weather_features = ['temp', 'atemp', 'hum', 'windspeed', 'temp_hum', 'temp_windspeed']
        time_features = ['hour', 'day', 'month', 'year', 'weekday', 'is_weekend',
                        'hour_sin', 'hour_cos', 'month_sin', 'month_cos', 'weekday_sin', 'weekday_cos']
        categorical_features = ['season', 'yr', 'mnth', 'hr', 'holiday', 'workingday', 'weathersit', 'is_rush_hour']
        lag_features = [col for col in all_columns if 'lag' in col]
        rolling_features = [col for col in all_columns if 'rolling' in col]
        
        return {
            'weather': weather_features,
            'time': time_features,
            'categorical': categorical_features,
            'lag': lag_features,
            'rolling': rolling_features,
            'target': 'cnt'
        }
