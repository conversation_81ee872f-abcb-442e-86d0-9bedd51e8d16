# 🚴‍♂️ Bike Sharing Demand Prediction System with Uncertainty Quantification

A comprehensive machine learning system for predicting bike sharing demand with advanced uncertainty quantification using cloud model theory. This system combines time series forecasting, deep learning, and decision support capabilities to optimize bike sharing operations.

## 🌟 Features

### Core Functionality
- **Data Processing & Feature Engineering**: Automated data cleaning, feature extraction, and engineering
- **Exploratory Data Analysis**: Comprehensive analysis of demand patterns and influencing factors
- **Multiple Forecasting Models**: ARIMA time series and LSTM deep learning models
- **Uncertainty Quantification**: Cloud model theory implementation for prediction uncertainty
- **Decision Support System**: Bike rebalancing optimization with uncertainty consideration
- **Comprehensive Evaluation**: Multiple metrics including PICP and MPIW for uncertainty assessment

### Advanced Features
- **Monte Carlo Dropout**: Uncertainty estimation for neural networks
- **Cloud Model Theory**: Novel approach to uncertainty quantification with Ex, En, He parameters
- **Multi-confidence Intervals**: 80%, 90%, and 95% prediction intervals
- **Rebalancing Simulation**: Station-level demand simulation with optimization
- **Risk Assessment**: Uncertainty impact analysis on operational decisions

## 📁 Project Structure

```
bike-sharing-prediction/
├── main.py                    # Main execution script
├── config.py                  # Configuration settings
├── requirements.txt           # Python dependencies
├── utils.py                   # Utility functions
├── data_processor.py          # Data processing and feature engineering
├── eda_analysis.py           # Exploratory data analysis
├── arima_model.py            # ARIMA time series model
├── lstm_model.py             # LSTM deep learning model
├── cloud_model.py            # Cloud model uncertainty quantification
├── model_evaluation.py       # Model evaluation and comparison
├── decision_support.py       # Decision support system
├── data/                     # Data directory
├── models/                   # Saved models directory
├── results/                  # Results and reports directory
├── plots/                    # Generated visualizations
└── README.md                 # This file
```

## 🚀 Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd bike-sharing-prediction

# Install dependencies
pip install -r requirements.txt
```

### 2. Run Complete Analysis

```bash
python main.py
```

This will execute the entire pipeline:
1. Download and process UCI Bike Sharing dataset
2. Perform exploratory data analysis
3. Train ARIMA and LSTM models
4. Quantify prediction uncertainty
5. Evaluate and compare models
6. Run decision support analysis
7. Generate comprehensive report

### 3. Individual Module Usage

```python
# Data processing
from data_processor import DataProcessor
processor = DataProcessor()
train_data, val_data, test_data = processor.split_data()

# ARIMA modeling
from arima_model import ARIMAPredictor
arima = ARIMAPredictor()
arima.fit(train_data)
predictions = arima.predict(len(test_data))

# LSTM modeling
from lstm_model import LSTMPredictor
lstm = LSTMPredictor()
lstm.fit(train_data, val_data, feature_columns)
predictions = lstm.predict(test_data, feature_columns)

# Uncertainty quantification
from cloud_model import UncertaintyQuantifier
uq = UncertaintyQuantifier()
uq.fit_uncertainty_model('LSTM', y_true, y_pred)
uncertainty_bounds = uq.predict_with_uncertainty('LSTM', predictions)
```

## 📊 Dataset

The system uses the **UCI Bike Sharing Dataset** which contains:
- **17,379 hourly records** from 2011-2012
- **Weather information**: temperature, humidity, wind speed, weather conditions
- **Temporal features**: hour, day, month, season, holiday indicators
- **Target variable**: bike rental count (cnt)

### Automatic Data Download
The system automatically downloads and processes the dataset on first run.

## 🧠 Models

### 1. ARIMA Model
- **Seasonal ARIMA** with 24-hour seasonality
- **Automatic order selection** using AIC criterion
- **Residual analysis** for model validation
- **Confidence intervals** for predictions

### 2. LSTM Model
- **Multi-layer LSTM** with dropout regularization
- **Feature engineering**: weather, temporal, lag, and rolling features
- **Monte Carlo Dropout** for uncertainty estimation
- **Early stopping** and model checkpointing

### 3. Cloud Model Theory
- **Ex (Expectation)**: Expected value of predictions
- **En (Entropy)**: Uncertainty measure of the concept
- **He (Hyper-entropy)**: Uncertainty of the uncertainty measure
- **Membership functions** for uncertainty quantification

## 📈 Evaluation Metrics

### Prediction Accuracy
- **MAE**: Mean Absolute Error
- **RMSE**: Root Mean Square Error
- **MAPE**: Mean Absolute Percentage Error
- **R²**: Coefficient of Determination
- **Directional Accuracy**: Trend prediction accuracy

### Uncertainty Quantification
- **PICP**: Prediction Interval Coverage Probability
- **MPIW**: Mean Prediction Interval Width
- **NMPIW**: Normalized Mean Prediction Interval Width
- **Calibration Analysis**: Reliability of uncertainty bounds

## 🎯 Decision Support System

### Bike Rebalancing Optimization
- **Station-level demand prediction** with uncertainty
- **Optimization algorithms** for bike redistribution
- **Cost-benefit analysis** including rebalancing costs
- **Risk assessment** under uncertainty

### Performance Metrics
- **Satisfaction Rate**: Percentage of demand met
- **Shortage Analysis**: Unmet demand quantification
- **Resource Utilization**: Efficiency metrics
- **Uncertainty Impact**: Effect of prediction uncertainty on operations

## 📋 Configuration

Key parameters in `config.py`:

```python
# Model parameters
ARIMA_ORDER = (2, 1, 2)
LSTM_UNITS = 50
LSTM_SEQUENCE_LENGTH = 24

# Uncertainty quantification
CONFIDENCE_LEVELS = [0.8, 0.9, 0.95]
CLOUD_MODEL_SAMPLES = 1000

# Data split
TRAIN_RATIO = 0.8
VALIDATION_RATIO = 0.1
TEST_RATIO = 0.1
```

## 📊 Output Files

### Results Directory
- `final_comprehensive_report.json`: Complete analysis summary
- `evaluation_report.json`: Model comparison results
- `detailed_results.json`: Detailed performance metrics
- `predictions.json`: Model predictions
- `uncertainty_bounds.json`: Uncertainty quantification results
- `operational_recommendations.json`: Decision support recommendations

### Plots Directory
- `demand_patterns.png`: Temporal demand analysis
- `weather_impact.png`: Weather influence on demand
- `arima_predictions.png`: ARIMA model results
- `lstm_predictions.png`: LSTM model results
- `uncertainty_distribution_*.png`: Uncertainty analysis
- `predictions_comparison.png`: Model comparison
- `strategy_comparison.png`: Decision support analysis

### Models Directory
- `arima_model.pkl`: Trained ARIMA model
- `lstm_model.h5`: Trained LSTM model
- `lstm_scalers.pkl`: Feature scalers for LSTM

## 🔬 Research Applications

This system supports research in:
- **Time series forecasting** with uncertainty
- **Urban mobility** and transportation planning
- **Operations research** and optimization
- **Risk management** in shared mobility systems
- **Machine learning** uncertainty quantification

## 🛠️ Customization

### Adding New Models
```python
# Implement in new file (e.g., prophet_model.py)
class ProphetPredictor:
    def fit(self, train_data):
        # Implementation
        pass
    
    def predict(self, steps):
        # Implementation
        pass

# Add to main.py evaluation
evaluator.add_model_results('Prophet', y_true, y_pred, uncertainty_bounds)
```

### Custom Uncertainty Methods
```python
# Extend cloud_model.py
class CustomUncertaintyModel:
    def fit_uncertainty_model(self, errors):
        # Custom implementation
        pass
    
    def predict_with_uncertainty(self, predictions):
        # Custom implementation
        pass
```

## 📚 Dependencies

- **pandas**: Data manipulation and analysis
- **numpy**: Numerical computing
- **scikit-learn**: Machine learning utilities
- **tensorflow**: Deep learning framework
- **statsmodels**: Statistical modeling
- **matplotlib/seaborn**: Data visualization
- **scipy**: Scientific computing

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Implement your changes
4. Add tests and documentation
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For questions, issues, or contributions:
- Create an issue on GitHub
- Contact the development team
- Check the documentation

## 🎉 Acknowledgments

- UCI Machine Learning Repository for the bike sharing dataset
- Research community for cloud model theory development
- Open source contributors for the underlying libraries

---

**Built with ❤️ for sustainable urban mobility and data-driven decision making**
