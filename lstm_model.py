"""
LSTM model implementation for bike sharing demand prediction
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint
import os
import config
from utils import create_sequences, calculate_metrics, set_random_seed
from data_processor import DataProcessor

class LSTMPredictor:
    """LSTM model for time series forecasting"""
    
    def __init__(self, sequence_length=None, units=None, dropout=None):
        self.sequence_length = sequence_length or config.LSTM_SEQUENCE_LENGTH
        self.units = units or config.LSTM_UNITS
        self.dropout = dropout or config.LSTM_DROPOUT
        self.model = None
        self.scaler = MinMaxScaler()
        self.feature_scaler = StandardScaler()
        self.data_processor = DataProcessor()
        set_random_seed()
        
        # Set TensorFlow random seed
        tf.random.set_seed(config.RANDOM_SEED)
    
    def prepare_data(self, data, feature_columns=None):
        """Prepare data for LSTM training"""
        if isinstance(data, pd.DataFrame):
            # Use all numerical features if not specified
            if feature_columns is None:
                feature_columns = data.select_dtypes(include=[np.number]).columns.tolist()
                if 'cnt' in feature_columns:
                    feature_columns.remove('cnt')
            
            # Extract features and target
            X = data[feature_columns].values
            y = data['cnt'].values
        else:
            X = data
            y = data
        
        # Scale features and target
        X_scaled = self.feature_scaler.fit_transform(X)
        y_scaled = self.scaler.fit_transform(y.reshape(-1, 1)).flatten()
        
        # Create sequences
        X_seq, y_seq = create_sequences(
            np.column_stack([X_scaled, y_scaled]), 
            self.sequence_length
        )
        
        # Separate features and target in sequences
        X_sequences = X_seq[:, :, :-1]  # All features except target
        y_sequences = y_seq  # Target values
        
        return X_sequences, y_sequences
    
    def prepare_test_data(self, data, feature_columns=None):
        """Prepare test data using fitted scalers"""
        if isinstance(data, pd.DataFrame):
            if feature_columns is None:
                feature_columns = data.select_dtypes(include=[np.number]).columns.tolist()
                if 'cnt' in feature_columns:
                    feature_columns.remove('cnt')
            
            X = data[feature_columns].values
            y = data['cnt'].values
        else:
            X = data
            y = data
        
        # Scale using fitted scalers
        X_scaled = self.feature_scaler.transform(X)
        y_scaled = self.scaler.transform(y.reshape(-1, 1)).flatten()
        
        # Create sequences
        X_seq, y_seq = create_sequences(
            np.column_stack([X_scaled, y_scaled]), 
            self.sequence_length
        )
        
        X_sequences = X_seq[:, :, :-1]
        y_sequences = y_seq
        
        return X_sequences, y_sequences
    
    def build_model(self, input_shape):
        """Build LSTM model architecture"""
        model = Sequential([
            LSTM(self.units, return_sequences=True, input_shape=input_shape),
            Dropout(self.dropout),
            LSTM(self.units, return_sequences=True),
            Dropout(self.dropout),
            LSTM(self.units, return_sequences=False),
            Dropout(self.dropout),
            Dense(50, activation='relu'),
            Dense(1)
        ])
        
        model.compile(
            optimizer='adam',
            loss='mse',
            metrics=['mae']
        )
        
        return model
    
    def fit(self, train_data, val_data=None, feature_columns=None):
        """Fit LSTM model"""
        print("=== Preparing Training Data ===")
        
        # Prepare training data
        X_train, y_train = self.prepare_data(train_data, feature_columns)
        
        print(f"Training sequences shape: {X_train.shape}")
        print(f"Training targets shape: {y_train.shape}")
        
        # Prepare validation data if provided
        if val_data is not None:
            X_val, y_val = self.prepare_test_data(val_data, feature_columns)
            validation_data = (X_val, y_val)
            print(f"Validation sequences shape: {X_val.shape}")
        else:
            validation_data = None
        
        # Build model
        print("=== Building LSTM Model ===")
        input_shape = (X_train.shape[1], X_train.shape[2])
        self.model = self.build_model(input_shape)
        
        print(self.model.summary())
        
        # Define callbacks
        callbacks = [
            EarlyStopping(
                monitor='val_loss' if validation_data else 'loss',
                patience=10,
                restore_best_weights=True
            ),
            ModelCheckpoint(
                os.path.join(config.MODELS_DIR, 'lstm_best_model.h5'),
                monitor='val_loss' if validation_data else 'loss',
                save_best_only=True
            )
        ]
        
        # Train model
        print("=== Training LSTM Model ===")
        history = self.model.fit(
            X_train, y_train,
            epochs=config.LSTM_EPOCHS,
            batch_size=config.LSTM_BATCH_SIZE,
            validation_data=validation_data,
            callbacks=callbacks,
            verbose=1
        )
        
        # Plot training history
        self.plot_training_history(history)
        
        return history
    
    def predict(self, data, feature_columns=None):
        """Make predictions"""
        if self.model is None:
            raise ValueError("Model must be fitted before making predictions")
        
        # Prepare data
        X_test, _ = self.prepare_test_data(data, feature_columns)
        
        # Make predictions
        y_pred_scaled = self.model.predict(X_test)
        
        # Inverse transform predictions
        y_pred = self.scaler.inverse_transform(y_pred_scaled).flatten()
        
        return y_pred
    
    def predict_with_uncertainty(self, data, feature_columns=None, n_samples=100):
        """Make predictions with uncertainty estimation using Monte Carlo Dropout"""
        if self.model is None:
            raise ValueError("Model must be fitted before making predictions")
        
        # Prepare data
        X_test, _ = self.prepare_test_data(data, feature_columns)
        
        # Enable dropout during inference for uncertainty estimation
        predictions = []
        
        for _ in range(n_samples):
            # Make prediction with dropout enabled
            y_pred_scaled = self.model(X_test, training=True)
            y_pred = self.scaler.inverse_transform(y_pred_scaled.numpy()).flatten()
            predictions.append(y_pred)
        
        predictions = np.array(predictions)
        
        # Calculate statistics
        mean_pred = np.mean(predictions, axis=0)
        std_pred = np.std(predictions, axis=0)
        
        # Calculate confidence intervals
        confidence_intervals = {}
        for alpha in config.CONFIDENCE_LEVELS:
            z_score = 1.96 if alpha == 0.95 else (1.645 if alpha == 0.9 else 1.282)
            lower_bound = mean_pred - z_score * std_pred
            upper_bound = mean_pred + z_score * std_pred
            
            confidence_intervals[f'confidence_{int(alpha*100)}'] = {
                'forecast': mean_pred,
                'lower_bound': lower_bound,
                'upper_bound': upper_bound,
                'std': std_pred
            }
        
        return confidence_intervals
    
    def evaluate(self, test_data, feature_columns=None):
        """Evaluate model performance"""
        # Get true values
        if isinstance(test_data, pd.DataFrame):
            y_true = test_data['cnt'].values
        else:
            y_true = test_data
        
        # Make predictions
        y_pred = self.predict(test_data, feature_columns)
        
        # Align lengths (predictions might be shorter due to sequence creation)
        min_length = min(len(y_true), len(y_pred))
        y_true = y_true[-min_length:]
        y_pred = y_pred[-min_length:]
        
        # Calculate metrics
        metrics = calculate_metrics(y_true, y_pred)
        
        print("=== LSTM Model Evaluation ===")
        for metric, value in metrics.items():
            print(f"{metric}: {value:.4f}")
        
        return metrics, y_pred, y_true
    
    def plot_training_history(self, history):
        """Plot training history"""
        fig, axes = plt.subplots(1, 2, figsize=(15, 5))
        
        # Plot loss
        axes[0].plot(history.history['loss'], label='Training Loss')
        if 'val_loss' in history.history:
            axes[0].plot(history.history['val_loss'], label='Validation Loss')
        axes[0].set_title('Model Loss')
        axes[0].set_xlabel('Epoch')
        axes[0].set_ylabel('Loss')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        # Plot MAE
        axes[1].plot(history.history['mae'], label='Training MAE')
        if 'val_mae' in history.history:
            axes[1].plot(history.history['val_mae'], label='Validation MAE')
        axes[1].set_title('Model MAE')
        axes[1].set_xlabel('Epoch')
        axes[1].set_ylabel('MAE')
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(f'{config.PLOTS_DIR}/lstm_training_history.png', dpi=config.DPI, bbox_inches='tight')
        plt.show()
    
    def plot_predictions(self, test_data, predictions, y_true, feature_columns=None):
        """Plot predictions vs actual values"""
        plt.figure(figsize=(15, 8))
        
        # Create time index for plotting
        if isinstance(test_data, pd.DataFrame):
            time_index = test_data.index[-len(predictions):]
        else:
            time_index = range(len(predictions))
        
        plt.plot(time_index, y_true, label='Actual', color='blue', alpha=0.7)
        plt.plot(time_index, predictions, label='LSTM Predictions', color='red', alpha=0.7)
        
        plt.title('LSTM Model Predictions vs Actual')
        plt.xlabel('Time')
        plt.ylabel('Demand')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(f'{config.PLOTS_DIR}/lstm_predictions.png', dpi=config.DPI, bbox_inches='tight')
        plt.show()
    
    def save_model(self, filename='lstm_model.h5'):
        """Save trained model"""
        if self.model is None:
            raise ValueError("No model to save")
        
        filepath = os.path.join(config.MODELS_DIR, filename)
        self.model.save(filepath)
        
        # Save scalers
        import pickle
        scaler_path = os.path.join(config.MODELS_DIR, 'lstm_scalers.pkl')
        with open(scaler_path, 'wb') as f:
            pickle.dump({
                'target_scaler': self.scaler,
                'feature_scaler': self.feature_scaler
            }, f)
        
        print(f"Model saved to {filepath}")
        print(f"Scalers saved to {scaler_path}")
    
    def load_model(self, filename='lstm_model.h5'):
        """Load trained model"""
        filepath = os.path.join(config.MODELS_DIR, filename)
        
        if os.path.exists(filepath):
            self.model = tf.keras.models.load_model(filepath)
            
            # Load scalers
            import pickle
            scaler_path = os.path.join(config.MODELS_DIR, 'lstm_scalers.pkl')
            with open(scaler_path, 'rb') as f:
                scalers = pickle.load(f)
                self.scaler = scalers['target_scaler']
                self.feature_scaler = scalers['feature_scaler']
            
            print(f"Model loaded from {filepath}")
        else:
            raise FileNotFoundError(f"Model file not found: {filepath}")

if __name__ == "__main__":
    # Example usage
    data_processor = DataProcessor()
    train_data, val_data, test_data = data_processor.split_data()
    
    # Get feature columns
    feature_info = data_processor.get_feature_columns()
    feature_columns = (feature_info['weather'] + feature_info['time'] + 
                      feature_info['categorical'] + feature_info['lag'] + 
                      feature_info['rolling'])
    
    # Initialize and fit LSTM model
    lstm = LSTMPredictor()
    lstm.fit(train_data, val_data, feature_columns)
    
    # Evaluate model
    metrics, predictions, y_true = lstm.evaluate(test_data, feature_columns)
    
    # Plot predictions
    lstm.plot_predictions(test_data, predictions, y_true, feature_columns)
    
    # Save model
    lstm.save_model()
