"""
Configuration file for bike sharing demand prediction system
"""
import os

# Data configuration
DATA_URL = "https://archive.ics.uci.edu/ml/machine-learning-databases/00275/Bike-Sharing-Dataset.zip"
DATA_DIR = "data"
RAW_DATA_FILE = "hour.csv"
PROCESSED_DATA_FILE = "processed_data.csv"

# Model configuration
MODELS_DIR = "models"
RESULTS_DIR = "results"
PLOTS_DIR = "plots"

# Time series configuration
TRAIN_RATIO = 0.8
VALIDATION_RATIO = 0.1
TEST_RATIO = 0.1

# ARIMA configuration
ARIMA_ORDER = (2, 1, 2)
ARIMA_SEASONAL_ORDER = (1, 1, 1, 24)  # 24-hour seasonality

# LSTM configuration
LSTM_SEQUENCE_LENGTH = 24  # Use 24 hours to predict next hour
LSTM_UNITS = 50
LSTM_DROPOUT = 0.2
LSTM_EPOCHS = 100
LSTM_BATCH_SIZE = 32
LSTM_VALIDATION_SPLIT = 0.2

# Uncertainty quantification configuration
CONFIDENCE_LEVELS = [0.8, 0.9, 0.95]
CLOUD_MODEL_SAMPLES = 1000

# Feature engineering configuration
WEATHER_FEATURES = ['temp', 'atemp', 'hum', 'windspeed']
TIME_FEATURES = ['hour', 'day', 'month', 'year', 'weekday']
CATEGORICAL_FEATURES = ['season', 'yr', 'mnth', 'hr', 'holiday', 'weekday', 'workingday', 'weathersit']

# Evaluation metrics
METRICS = ['MAE', 'RMSE', 'MAPE', 'PICP', 'MPIW']

# Visualization configuration
FIGURE_SIZE = (12, 8)
DPI = 300
PLOT_STYLE = 'default'

# Random seed for reproducibility
RANDOM_SEED = 42

# Create directories if they don't exist
for directory in [DATA_DIR, MODELS_DIR, RESULTS_DIR, PLOTS_DIR]:
    os.makedirs(directory, exist_ok=True)
