"""
Main execution script for bike sharing demand prediction system
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import warnings
import os
import sys
from datetime import datetime

# Import custom modules
from data_processor import DataProcessor
from eda_analysis import EDAAnalyzer
from arima_model import <PERSON>IMAPredictor
from lstm_model import <PERSON><PERSON><PERSON><PERSON>ictor
from cloud_model import UncertaintyQuantifier
from model_evaluation import ModelEvaluator
from decision_support import DecisionSupportSystem
import config
from utils import set_random_seed, save_results

warnings.filterwarnings('ignore')

class BikeSharePredictionSystem:
    """Main system orchestrator"""
    
    def __init__(self):
        self.data_processor = DataProcessor()
        self.eda_analyzer = EDAAnalyzer()
        self.arima_model = ARIMAPredictor()
        self.lstm_model = LSTMPredictor()
        self.uncertainty_quantifier = UncertaintyQuantifier()
        self.evaluator = ModelEvaluator()
        self.decision_support = DecisionSupportSystem()
        
        # Data containers
        self.train_data = None
        self.val_data = None
        self.test_data = None
        self.feature_columns = None
        
        # Results containers
        self.arima_predictions = None
        self.lstm_predictions = None
        self.arima_uncertainty = None
        self.lstm_uncertainty = None
        
        set_random_seed()
    
    def run_data_processing(self):
        """Execute data processing pipeline"""
        print("=" * 60)
        print("STEP 1: DATA PROCESSING AND FEATURE ENGINEERING")
        print("=" * 60)
        
        # Load and process data
        self.data_processor.load_data()
        self.data_processor.clean_data()
        self.data_processor.engineer_features()
        
        # Split data
        self.train_data, self.val_data, self.test_data = self.data_processor.split_data()
        
        # Get feature columns
        feature_info = self.data_processor.get_feature_columns()
        self.feature_columns = (feature_info['weather'] + feature_info['time'] + 
                               feature_info['categorical'] + feature_info['lag'] + 
                               feature_info['rolling'])
        
        # Save processed data
        self.data_processor.save_processed_data()
        
        print("Data processing completed successfully!")
        print(f"Training data: {len(self.train_data)} samples")
        print(f"Validation data: {len(self.val_data)} samples")
        print(f"Test data: {len(self.test_data)} samples")
        print(f"Features: {len(self.feature_columns)} columns")
    
    def run_eda_analysis(self):
        """Execute exploratory data analysis"""
        print("\n" + "=" * 60)
        print("STEP 2: EXPLORATORY DATA ANALYSIS")
        print("=" * 60)
        
        self.eda_analyzer.generate_summary_report()
        print("EDA analysis completed successfully!")
    
    def run_arima_modeling(self):
        """Execute ARIMA modeling"""
        print("\n" + "=" * 60)
        print("STEP 3: ARIMA TIME SERIES MODELING")
        print("=" * 60)
        
        # Fit ARIMA model
        self.arima_model.fit(self.train_data)
        
        # Evaluate model
        arima_metrics, self.arima_predictions = self.arima_model.evaluate(self.test_data)
        
        # Plot predictions
        self.arima_model.plot_predictions(self.train_data, self.test_data, self.arima_predictions)
        
        # Residual analysis
        self.arima_model.residual_analysis()
        
        # Save model
        self.arima_model.save_model()
        
        print("ARIMA modeling completed successfully!")
        return arima_metrics
    
    def run_lstm_modeling(self):
        """Execute LSTM modeling"""
        print("\n" + "=" * 60)
        print("STEP 4: LSTM DEEP LEARNING MODELING")
        print("=" * 60)
        
        # Fit LSTM model
        self.lstm_model.fit(self.train_data, self.val_data, self.feature_columns)
        
        # Evaluate model
        lstm_metrics, self.lstm_predictions, y_true = self.lstm_model.evaluate(
            self.test_data, self.feature_columns
        )
        
        # Plot predictions
        self.lstm_model.plot_predictions(
            self.test_data, self.lstm_predictions, y_true, self.feature_columns
        )
        
        # Save model
        self.lstm_model.save_model()
        
        print("LSTM modeling completed successfully!")
        return lstm_metrics
    
    def run_uncertainty_quantification(self):
        """Execute uncertainty quantification"""
        print("\n" + "=" * 60)
        print("STEP 5: UNCERTAINTY QUANTIFICATION")
        print("=" * 60)
        
        # Get true values for uncertainty fitting
        y_true = self.test_data['cnt'].values
        
        # Align predictions with true values
        min_length = min(len(y_true), len(self.arima_predictions), len(self.lstm_predictions))
        y_true_aligned = y_true[-min_length:]
        arima_pred_aligned = self.arima_predictions[-min_length:]
        lstm_pred_aligned = self.lstm_predictions[-min_length:]
        
        # Fit uncertainty models
        self.uncertainty_quantifier.fit_uncertainty_model('ARIMA', y_true_aligned, arima_pred_aligned)
        self.uncertainty_quantifier.fit_uncertainty_model('LSTM', y_true_aligned, lstm_pred_aligned)
        
        # Generate uncertainty bounds
        self.arima_uncertainty = self.uncertainty_quantifier.predict_with_uncertainty(
            'ARIMA', arima_pred_aligned
        )
        self.lstm_uncertainty = self.uncertainty_quantifier.predict_with_uncertainty(
            'LSTM', lstm_pred_aligned
        )
        
        # Plot uncertainty distributions
        self.uncertainty_quantifier.plot_uncertainty_distribution('ARIMA')
        self.uncertainty_quantifier.plot_uncertainty_distribution('LSTM')
        
        # Compare uncertainty models
        self.uncertainty_quantifier.compare_uncertainty_models(['ARIMA', 'LSTM'])
        
        print("Uncertainty quantification completed successfully!")
    
    def run_model_evaluation(self):
        """Execute comprehensive model evaluation"""
        print("\n" + "=" * 60)
        print("STEP 6: MODEL EVALUATION AND COMPARISON")
        print("=" * 60)
        
        # Get true values
        y_true = self.test_data['cnt'].values
        min_length = min(len(y_true), len(self.arima_predictions), len(self.lstm_predictions))
        y_true_aligned = y_true[-min_length:]
        
        # Add model results to evaluator
        self.evaluator.add_model_results(
            'ARIMA', 
            y_true_aligned, 
            self.arima_predictions[-min_length:],
            self.arima_uncertainty
        )
        
        self.evaluator.add_model_results(
            'LSTM', 
            y_true_aligned, 
            self.lstm_predictions[-min_length:],
            self.lstm_uncertainty
        )
        
        # Generate comparison plots
        self.evaluator.plot_predictions_comparison()
        self.evaluator.plot_error_analysis()
        self.evaluator.plot_metrics_comparison()
        self.evaluator.plot_uncertainty_analysis()
        
        # Generate evaluation report
        evaluation_report = self.evaluator.generate_evaluation_report()
        
        # Save all results
        self.evaluator.save_all_results()
        
        print("Model evaluation completed successfully!")
        return evaluation_report
    
    def run_decision_support_analysis(self):
        """Execute decision support analysis"""
        print("\n" + "=" * 60)
        print("STEP 7: DECISION SUPPORT SYSTEM ANALYSIS")
        print("=" * 60)
        
        # Create sample station-level predictions (simplified for demonstration)
        n_stations = 10
        time_horizon = 24
        
        # Generate station-level predictions based on overall predictions
        station_predictions = {}
        station_uncertainty = {}
        
        for i in range(n_stations):
            # Distribute overall demand across stations with some variation
            station_factor = np.random.uniform(0.5, 1.5)
            base_demand = np.mean(self.lstm_predictions[-time_horizon:]) * station_factor / n_stations
            
            # Add hourly variation
            hourly_pattern = np.sin(np.linspace(0, 2*np.pi, time_horizon)) * base_demand * 0.3
            station_demand = np.maximum(0, base_demand + hourly_pattern + np.random.normal(0, base_demand*0.1, time_horizon))
            
            station_predictions[f'station_{i}'] = station_demand
            
            # Create uncertainty bounds
            uncertainty_factor = 0.2  # 20% uncertainty
            uncertainty = station_demand * uncertainty_factor
            
            station_uncertainty[f'station_{i}'] = {
                'confidence_95': {
                    'lower_bound': station_demand - 1.96 * uncertainty,
                    'upper_bound': station_demand + 1.96 * uncertainty
                }
            }
        
        # Define strategies to compare
        strategies = {
            'deterministic_strategy': {
                'predictions': station_predictions,
                'n_simulations': 30
            },
            'uncertainty_aware_strategy': {
                'predictions': station_predictions,
                'uncertainty_bounds': station_uncertainty,
                'n_simulations': 30
            }
        }
        
        # Compare strategies
        strategy_comparison = self.decision_support.compare_strategies(strategies)
        
        # Plot strategy comparison
        self.decision_support.plot_strategy_comparison()
        
        # Analyze uncertainty impact
        impact_results = self.decision_support.analyze_uncertainty_impact(station_predictions)
        
        # Generate recommendations
        recommendations = self.decision_support.generate_recommendations()
        
        print("Decision support analysis completed successfully!")
        return strategy_comparison, recommendations
    
    def generate_final_report(self, evaluation_report, recommendations):
        """Generate final comprehensive report"""
        print("\n" + "=" * 60)
        print("STEP 8: GENERATING FINAL REPORT")
        print("=" * 60)
        
        final_report = {
            'timestamp': datetime.now().isoformat(),
            'system_configuration': {
                'arima_order': config.ARIMA_ORDER,
                'lstm_units': config.LSTM_UNITS,
                'sequence_length': config.LSTM_SEQUENCE_LENGTH,
                'confidence_levels': config.CONFIDENCE_LEVELS
            },
            'data_summary': {
                'total_samples': len(self.data_processor.processed_data),
                'training_samples': len(self.train_data),
                'test_samples': len(self.test_data),
                'features_count': len(self.feature_columns)
            },
            'model_performance': evaluation_report,
            'operational_recommendations': recommendations,
            'key_findings': []
        }
        
        # Extract key findings
        best_model = evaluation_report['best_model_by_metric'].get('RMSE', 'Unknown')
        final_report['key_findings'].append(f"Best performing model: {best_model}")
        
        if 'ARIMA' in evaluation_report['model_comparison'] and 'LSTM' in evaluation_report['model_comparison']:
            arima_rmse = evaluation_report['model_comparison']['ARIMA']['RMSE']
            lstm_rmse = evaluation_report['model_comparison']['LSTM']['RMSE']
            improvement = ((arima_rmse - lstm_rmse) / arima_rmse) * 100
            final_report['key_findings'].append(f"LSTM vs ARIMA RMSE improvement: {improvement:.1f}%")
        
        # Save final report
        save_results(final_report, 'final_comprehensive_report.json')
        
        print("=== FINAL SYSTEM REPORT ===")
        print(f"Analysis completed at: {final_report['timestamp']}")
        print(f"Best model: {best_model}")
        print(f"Total samples processed: {final_report['data_summary']['total_samples']}")
        print(f"Features engineered: {final_report['data_summary']['features_count']}")
        print("\nAll results saved to results directory!")
        
        return final_report
    
    def run_complete_analysis(self):
        """Run the complete analysis pipeline"""
        print("🚴‍♂️ BIKE SHARING DEMAND PREDICTION SYSTEM")
        print("=" * 60)
        print("Starting comprehensive analysis...")
        
        try:
            # Step 1: Data Processing
            self.run_data_processing()
            
            # Step 2: EDA
            self.run_eda_analysis()
            
            # Step 3: ARIMA Modeling
            arima_metrics = self.run_arima_modeling()
            
            # Step 4: LSTM Modeling
            lstm_metrics = self.run_lstm_modeling()
            
            # Step 5: Uncertainty Quantification
            self.run_uncertainty_quantification()
            
            # Step 6: Model Evaluation
            evaluation_report = self.run_model_evaluation()
            
            # Step 7: Decision Support
            strategy_comparison, recommendations = self.run_decision_support_analysis()
            
            # Step 8: Final Report
            final_report = self.generate_final_report(evaluation_report, recommendations)
            
            print("\n" + "=" * 60)
            print("🎉 ANALYSIS COMPLETED SUCCESSFULLY!")
            print("=" * 60)
            print("Check the following directories for results:")
            print(f"📊 Plots: {config.PLOTS_DIR}/")
            print(f"📈 Models: {config.MODELS_DIR}/")
            print(f"📋 Results: {config.RESULTS_DIR}/")
            
            return final_report
            
        except Exception as e:
            print(f"\n❌ Error during analysis: {str(e)}")
            print("Please check the error and try again.")
            raise

if __name__ == "__main__":
    # Initialize and run the complete system
    system = BikeSharePredictionSystem()
    
    # Run complete analysis
    final_report = system.run_complete_analysis()
