# 📊 答辩图片使用指南

## 🎯 核心图片推荐（答辩必用）

### 1️⃣ **模型性能对比** - `metrics_comparison.png`
**用途**: 展示LSTM vs ARIMA的性能对比
**放置位置**: 实验结果部分（第3-4分钟）
**讲解要点**:
- "从图中可以看到，LSTM在MAE、RMSE、R²等关键指标上都显著优于ARIMA"
- "MAE从151.36降低到115.01，改进24%"
- "R²从-0.199提升到0.252，说明LSTM确实捕捉了数据规律"

### 2️⃣ **预测效果对比** - `predictions_comparison.png`
**用途**: 直观展示两个模型的预测效果
**放置位置**: 实验结果部分（第4-5分钟）
**讲解要点**:
- "蓝线是真实值，红线是预测值，灰色区域是95%置信区间"
- "可以看到LSTM的预测更贴近真实值，置信区间也更合理"

### 3️⃣ **不确定性分析** - `uncertainty_analysis.png`
**用途**: 展示不确定性量化的校准效果
**放置位置**: 技术方法部分（第6-7分钟）
**讲解要点**:
- "这张图展示了我们不确定性量化的校准效果"
- "95%置信区间的实际覆盖率达到97.4%，非常接近理论值"

### 4️⃣ **需求模式分析** - `demand_patterns.png`
**用途**: 展示数据特征和规律发现
**放置位置**: 数据分析部分（第2-3分钟）
**讲解要点**:
- "从图中可以看到明显的早晚高峰模式"
- "这些时间规律为我们的预测模型提供了重要特征"

---

## 📋 完整图片清单与用途

### 🔍 **数据分析类图片**
1. **`demand_patterns.png`** ⭐⭐⭐
   - 展示时间需求模式（小时、日、月、季节）
   - 证明数据的规律性和可预测性

2. **`weather_impact.png`** ⭐⭐
   - 展示天气对需求的影响
   - 说明特征工程的重要性

3. **`correlation_matrix.png`** ⭐⭐
   - 展示特征间的相关性
   - 支持特征选择的合理性

4. **`time_series_overview.png`** ⭐
   - 展示完整时间序列概览
   - 背景介绍时使用

5. **`workday_holiday_patterns.png`** ⭐
   - 工作日vs节假日模式对比
   - 补充说明数据复杂性

### 🤖 **模型性能类图片**
6. **`metrics_comparison.png`** ⭐⭐⭐
   - 核心性能指标对比
   - **必用图片**

7. **`predictions_comparison.png`** ⭐⭐⭐
   - 预测效果直观对比
   - **必用图片**

8. **`error_analysis.png`** ⭐⭐
   - 误差分布和散点图
   - 深入分析模型性能

9. **`lstm_training_history.png`** ⭐
   - LSTM训练过程
   - 展示模型收敛性

10. **`lstm_predictions.png`** ⭐
    - LSTM单独预测结果
    - 可选补充图片

### 🎯 **ARIMA模型分析**
11. **`arima_predictions.png`** ⭐
    - ARIMA预测结果
    - 对比时使用

12. **`arima_decomposition.png`** ⭐
    - 时间序列分解
    - 展示ARIMA建模过程

13. **`arima_acf_pacf.png`** ⭐
    - 自相关和偏自相关
    - 技术细节展示

14. **`arima_residual_analysis.png`** ⭐
    - 残差分析
    - 模型诊断

### 🌤️ **不确定性量化类图片**
15. **`uncertainty_analysis.png`** ⭐⭐⭐
    - 不确定性校准分析
    - **核心创新展示**

16. **`uncertainty_distribution_LSTM.png`** ⭐⭐
    - LSTM不确定性分布
    - 云模型效果展示

17. **`uncertainty_distribution_ARIMA.png`** ⭐⭐
    - ARIMA不确定性分布
    - 对比分析

18. **`uncertainty_models_comparison.png`** ⭐⭐
    - 两个模型不确定性对比
    - 突出LSTM优势

### 🚴‍♂️ **决策支持类图片**
19. **`strategy_comparison.png`** ⭐⭐
    - 运营策略对比
    - 实际应用价值

20. **`uncertainty_impact.png`** ⭐⭐
    - 不确定性对决策的影响
    - 系统价值展示

---

## 🎤 答辩中的图片使用策略

### **PPT结构建议**：

#### **第1部分：问题背景（2-3分钟）**
- 使用：`time_series_overview.png`
- 目的：展示问题复杂性

#### **第2部分：数据分析（2-3分钟）**
- 主图：`demand_patterns.png` ⭐⭐⭐
- 补充：`weather_impact.png`
- 目的：展示数据规律和特征工程

#### **第3部分：技术方法（3-4分钟）**
- 架构图：自制技术路线图
- 补充：`correlation_matrix.png`
- 目的：说明方法选择合理性

#### **第4部分：实验结果（4-5分钟）**
- 核心图：`metrics_comparison.png` ⭐⭐⭐
- 效果图：`predictions_comparison.png` ⭐⭐⭐
- 创新图：`uncertainty_analysis.png` ⭐⭐⭐
- 目的：展示优秀结果和创新点

#### **第5部分：应用价值（2-3分钟）**
- 应用图：`strategy_comparison.png`
- 影响图：`uncertainty_impact.png`
- 目的：展示实际价值

---

## 💡 图片使用技巧

### **图片质量优化**：
1. **分辨率**: 所有图片都是300 DPI，适合投影
2. **大小**: 建议在PPT中占屏幕的60-80%
3. **清晰度**: 确保文字和数字清晰可读

### **讲解技巧**：
1. **指向关键点**: 用激光笔指向关键数据
2. **逐步解释**: 不要一次性说完所有内容
3. **数据强调**: 重点强调改进百分比
4. **对比突出**: 明确指出LSTM vs ARIMA的差异

### **时间控制**：
- 每张核心图片讲解1-2分钟
- 补充图片30秒-1分钟
- 预留时间回答图片相关问题

---

## 🎯 必用图片清单（5张）

如果时间有限，至少要用这5张图片：

1. **`demand_patterns.png`** - 展示数据规律
2. **`metrics_comparison.png`** - 性能对比（核心）
3. **`predictions_comparison.png`** - 预测效果（核心）
4. **`uncertainty_analysis.png`** - 不确定性校准（创新）
5. **`strategy_comparison.png`** - 应用价值

---

## 📱 图片文件使用方法

### **插入PPT**：
1. 复制 `plots/` 文件夹到PPT同目录
2. 在PPT中插入图片：插入 → 图片 → 此设备
3. 选择对应的 `.png` 文件

### **备用方案**：
- 准备图片的纸质打印版
- 在平板或笔记本上准备备用图片
- 确保图片文件在多个设备上都能访问

### **演示建议**：
- 提前测试所有图片显示效果
- 准备图片的简化版本（如果投影不清晰）
- 为每张图片准备30秒的核心讲解词

祝您答辩成功！🎉
