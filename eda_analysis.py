"""
Exploratory Data Analysis for bike sharing demand prediction
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import config
from data_processor import DataProcessor

class EDAAnalyzer:
    """Exploratory Data Analysis class"""
    
    def __init__(self):
        self.data_processor = DataProcessor()
        self.data = None
        plt.style.use(config.PLOT_STYLE)
    
    def load_data(self):
        """Load and prepare data for analysis"""
        self.data = self.data_processor.load_processed_data()
        return self.data
    
    def basic_statistics(self):
        """Generate basic statistics"""
        if self.data is None:
            self.load_data()
        
        print("=== BASIC STATISTICS ===")
        print(f"Dataset shape: {self.data.shape}")
        print(f"Date range: {self.data.index.min()} to {self.data.index.max()}")
        print(f"Total duration: {self.data.index.max() - self.data.index.min()}")
        
        print("\n=== TARGET VARIABLE STATISTICS ===")
        print(self.data['cnt'].describe())
        
        print("\n=== MISSING VALUES ===")
        missing = self.data.isnull().sum()
        if missing.sum() == 0:
            print("No missing values found!")
        else:
            print(missing[missing > 0])
    
    def plot_demand_patterns(self):
        """Plot various demand patterns"""
        if self.data is None:
            self.load_data()
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # Daily pattern
        hourly_avg = self.data.groupby('hour')['cnt'].mean()
        axes[0, 0].plot(hourly_avg.index, hourly_avg.values, marker='o')
        axes[0, 0].set_title('Average Demand by Hour of Day')
        axes[0, 0].set_xlabel('Hour')
        axes[0, 0].set_ylabel('Average Demand')
        axes[0, 0].grid(True, alpha=0.3)
        
        # Weekly pattern
        weekly_avg = self.data.groupby('weekday')['cnt'].mean()
        weekday_names = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        axes[0, 1].bar(range(7), weekly_avg.values)
        axes[0, 1].set_title('Average Demand by Day of Week')
        axes[0, 1].set_xlabel('Day of Week')
        axes[0, 1].set_ylabel('Average Demand')
        axes[0, 1].set_xticks(range(7))
        axes[0, 1].set_xticklabels(weekday_names)
        
        # Monthly pattern
        monthly_avg = self.data.groupby('month')['cnt'].mean()
        axes[1, 0].plot(monthly_avg.index, monthly_avg.values, marker='o')
        axes[1, 0].set_title('Average Demand by Month')
        axes[1, 0].set_xlabel('Month')
        axes[1, 0].set_ylabel('Average Demand')
        axes[1, 0].grid(True, alpha=0.3)
        
        # Seasonal pattern
        seasonal_avg = self.data.groupby('season')['cnt'].mean()
        season_names = ['Spring', 'Summer', 'Fall', 'Winter']
        axes[1, 1].bar(range(1, 5), seasonal_avg.values)
        axes[1, 1].set_title('Average Demand by Season')
        axes[1, 1].set_xlabel('Season')
        axes[1, 1].set_ylabel('Average Demand')
        axes[1, 1].set_xticks(range(1, 5))
        axes[1, 1].set_xticklabels(season_names)
        
        plt.tight_layout()
        plt.savefig(f'{config.PLOTS_DIR}/demand_patterns.png', dpi=config.DPI, bbox_inches='tight')
        plt.show()
    
    def plot_weather_impact(self):
        """Plot weather impact on demand"""
        if self.data is None:
            self.load_data()
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # Temperature vs Demand
        axes[0, 0].scatter(self.data['temp'], self.data['cnt'], alpha=0.5)
        axes[0, 0].set_title('Temperature vs Demand')
        axes[0, 0].set_xlabel('Normalized Temperature')
        axes[0, 0].set_ylabel('Demand')
        
        # Humidity vs Demand
        axes[0, 1].scatter(self.data['hum'], self.data['cnt'], alpha=0.5)
        axes[0, 1].set_title('Humidity vs Demand')
        axes[0, 1].set_xlabel('Normalized Humidity')
        axes[0, 1].set_ylabel('Demand')
        
        # Wind Speed vs Demand
        axes[1, 0].scatter(self.data['windspeed'], self.data['cnt'], alpha=0.5)
        axes[1, 0].set_title('Wind Speed vs Demand')
        axes[1, 0].set_xlabel('Normalized Wind Speed')
        axes[1, 0].set_ylabel('Demand')
        
        # Weather Situation vs Demand
        weather_avg = self.data.groupby('weathersit')['cnt'].mean()
        weather_labels = ['Clear', 'Mist/Cloudy', 'Light Snow/Rain', 'Heavy Rain/Snow']
        axes[1, 1].bar(weather_avg.index, weather_avg.values)
        axes[1, 1].set_title('Average Demand by Weather Situation')
        axes[1, 1].set_xlabel('Weather Situation')
        axes[1, 1].set_ylabel('Average Demand')
        axes[1, 1].set_xticks(weather_avg.index)
        axes[1, 1].set_xticklabels([weather_labels[i-1] for i in weather_avg.index], rotation=45)
        
        plt.tight_layout()
        plt.savefig(f'{config.PLOTS_DIR}/weather_impact.png', dpi=config.DPI, bbox_inches='tight')
        plt.show()
    
    def plot_correlation_matrix(self):
        """Plot correlation matrix of features"""
        if self.data is None:
            self.load_data()
        
        # Select numerical features for correlation
        numerical_features = ['cnt', 'temp', 'atemp', 'hum', 'windspeed', 'casual', 'registered']
        correlation_matrix = self.data[numerical_features].corr()
        
        plt.figure(figsize=(10, 8))
        sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0,
                   square=True, linewidths=0.5)
        plt.title('Correlation Matrix of Key Features')
        plt.tight_layout()
        plt.savefig(f'{config.PLOTS_DIR}/correlation_matrix.png', dpi=config.DPI, bbox_inches='tight')
        plt.show()
    
    def plot_time_series_overview(self):
        """Plot time series overview"""
        if self.data is None:
            self.load_data()
        
        fig, axes = plt.subplots(3, 1, figsize=(15, 12))
        
        # Full time series
        axes[0].plot(self.data.index, self.data['cnt'])
        axes[0].set_title('Bike Sharing Demand Over Time')
        axes[0].set_ylabel('Demand')
        axes[0].grid(True, alpha=0.3)
        
        # Monthly aggregation
        monthly_data = self.data.resample('M')['cnt'].mean()
        axes[1].plot(monthly_data.index, monthly_data.values, marker='o')
        axes[1].set_title('Monthly Average Demand')
        axes[1].set_ylabel('Average Demand')
        axes[1].grid(True, alpha=0.3)
        
        # Daily aggregation
        daily_data = self.data.resample('D')['cnt'].sum()
        axes[2].plot(daily_data.index, daily_data.values)
        axes[2].set_title('Daily Total Demand')
        axes[2].set_xlabel('Date')
        axes[2].set_ylabel('Total Demand')
        axes[2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(f'{config.PLOTS_DIR}/time_series_overview.png', dpi=config.DPI, bbox_inches='tight')
        plt.show()
    
    def plot_workday_vs_holiday(self):
        """Compare workday vs holiday/weekend patterns"""
        if self.data is None:
            self.load_data()
        
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        # Workday vs Non-workday hourly pattern
        workday_hourly = self.data[self.data['workingday'] == 1].groupby('hour')['cnt'].mean()
        non_workday_hourly = self.data[self.data['workingday'] == 0].groupby('hour')['cnt'].mean()
        
        axes[0].plot(workday_hourly.index, workday_hourly.values, label='Workday', marker='o')
        axes[0].plot(non_workday_hourly.index, non_workday_hourly.values, label='Non-workday', marker='s')
        axes[0].set_title('Hourly Demand: Workday vs Non-workday')
        axes[0].set_xlabel('Hour')
        axes[0].set_ylabel('Average Demand')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        # Holiday vs Non-holiday
        holiday_avg = self.data.groupby('holiday')['cnt'].mean()
        axes[1].bar(['Non-holiday', 'Holiday'], holiday_avg.values)
        axes[1].set_title('Average Demand: Holiday vs Non-holiday')
        axes[1].set_ylabel('Average Demand')
        
        plt.tight_layout()
        plt.savefig(f'{config.PLOTS_DIR}/workday_holiday_patterns.png', dpi=config.DPI, bbox_inches='tight')
        plt.show()
    
    def generate_summary_report(self):
        """Generate comprehensive EDA summary report"""
        if self.data is None:
            self.load_data()
        
        print("=== COMPREHENSIVE EDA REPORT ===")
        
        # Basic statistics
        self.basic_statistics()
        
        # Key insights
        print("\n=== KEY INSIGHTS ===")
        
        # Peak hours
        hourly_avg = self.data.groupby('hour')['cnt'].mean()
        peak_hours = hourly_avg.nlargest(3).index.tolist()
        print(f"Peak demand hours: {peak_hours}")
        
        # Best weather for biking
        weather_avg = self.data.groupby('weathersit')['cnt'].mean()
        best_weather = weather_avg.idxmax()
        print(f"Best weather situation for biking: {best_weather}")
        
        # Seasonal preferences
        seasonal_avg = self.data.groupby('season')['cnt'].mean()
        best_season = seasonal_avg.idxmax()
        season_names = {1: 'Spring', 2: 'Summer', 3: 'Fall', 4: 'Winter'}
        print(f"Best season for biking: {season_names[best_season]}")
        
        # Temperature correlation
        temp_corr = self.data['temp'].corr(self.data['cnt'])
        print(f"Temperature correlation with demand: {temp_corr:.3f}")
        
        # Workday vs weekend
        workday_avg = self.data[self.data['workingday'] == 1]['cnt'].mean()
        weekend_avg = self.data[self.data['workingday'] == 0]['cnt'].mean()
        print(f"Average workday demand: {workday_avg:.1f}")
        print(f"Average weekend demand: {weekend_avg:.1f}")
        
        # Generate all plots
        print("\n=== GENERATING VISUALIZATIONS ===")
        self.plot_demand_patterns()
        self.plot_weather_impact()
        self.plot_correlation_matrix()
        self.plot_time_series_overview()
        self.plot_workday_vs_holiday()
        
        print("EDA analysis completed! All plots saved to plots directory.")

if __name__ == "__main__":
    analyzer = EDAAnalyzer()
    analyzer.generate_summary_report()
