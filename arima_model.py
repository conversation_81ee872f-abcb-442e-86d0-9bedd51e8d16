"""
ARIMA model implementation for bike sharing demand prediction
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.tsa.stattools import adfuller
from statsmodels.graphics.tsaplots import plot_acf, plot_pacf
from sklearn.metrics import mean_absolute_error, mean_squared_error
import warnings
import pickle
import os
import config
from utils import calculate_metrics, set_random_seed
from data_processor import DataProcessor

warnings.filterwarnings('ignore')

class ARIMAPredictor:
    """ARIMA model for time series forecasting"""
    
    def __init__(self, order=None, seasonal_order=None):
        self.order = order or config.ARIMA_ORDER
        self.seasonal_order = seasonal_order or config.ARIMA_SEASONAL_ORDER
        self.model = None
        self.fitted_model = None
        self.data_processor = DataProcessor()
        set_random_seed()
    
    def check_stationarity(self, timeseries, title="Time Series"):
        """Check stationarity of time series using Augmented Dickey-Fuller test"""
        print(f"=== Stationarity Test for {title} ===")
        
        # Perform Augmented Dickey-Fuller test
        result = adfuller(timeseries.dropna())
        
        print(f'ADF Statistic: {result[0]:.6f}')
        print(f'p-value: {result[1]:.6f}')
        print('Critical Values:')
        for key, value in result[4].items():
            print(f'\t{key}: {value:.3f}')
        
        if result[1] <= 0.05:
            print("Result: Time series is stationary")
            return True
        else:
            print("Result: Time series is non-stationary")
            return False
    
    def plot_decomposition(self, timeseries, title="Time Series Decomposition"):
        """Plot seasonal decomposition"""
        plt.figure(figsize=(15, 12))
        
        # Perform seasonal decomposition
        decomposition = seasonal_decompose(timeseries, model='additive', period=24)
        
        # Plot components
        plt.subplot(4, 1, 1)
        plt.plot(decomposition.observed)
        plt.title('Original Time Series')
        plt.grid(True, alpha=0.3)
        
        plt.subplot(4, 1, 2)
        plt.plot(decomposition.trend)
        plt.title('Trend')
        plt.grid(True, alpha=0.3)
        
        plt.subplot(4, 1, 3)
        plt.plot(decomposition.seasonal)
        plt.title('Seasonal')
        plt.grid(True, alpha=0.3)
        
        plt.subplot(4, 1, 4)
        plt.plot(decomposition.resid)
        plt.title('Residual')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(f'{config.PLOTS_DIR}/arima_decomposition.png', dpi=config.DPI, bbox_inches='tight')
        plt.show()
        
        return decomposition
    
    def plot_acf_pacf(self, timeseries, lags=40):
        """Plot ACF and PACF for model order selection"""
        fig, axes = plt.subplots(2, 1, figsize=(12, 8))
        
        # Plot ACF
        plot_acf(timeseries.dropna(), lags=lags, ax=axes[0])
        axes[0].set_title('Autocorrelation Function (ACF)')
        
        # Plot PACF
        plot_pacf(timeseries.dropna(), lags=lags, ax=axes[1])
        axes[1].set_title('Partial Autocorrelation Function (PACF)')
        
        plt.tight_layout()
        plt.savefig(f'{config.PLOTS_DIR}/arima_acf_pacf.png', dpi=config.DPI, bbox_inches='tight')
        plt.show()
    
    def auto_arima_order_selection(self, timeseries, max_p=5, max_d=2, max_q=5):
        """Automatic ARIMA order selection using AIC"""
        print("=== Automatic ARIMA Order Selection ===")
        
        best_aic = np.inf
        best_order = None
        best_seasonal_order = None
        
        # Grid search for best parameters
        for p in range(max_p + 1):
            for d in range(max_d + 1):
                for q in range(max_q + 1):
                    for P in range(2):
                        for D in range(2):
                            for Q in range(2):
                                try:
                                    order = (p, d, q)
                                    seasonal_order = (P, D, Q, 24)
                                    
                                    model = ARIMA(timeseries, order=order, seasonal_order=seasonal_order)
                                    fitted_model = model.fit()
                                    
                                    if fitted_model.aic < best_aic:
                                        best_aic = fitted_model.aic
                                        best_order = order
                                        best_seasonal_order = seasonal_order
                                        
                                except:
                                    continue
        
        print(f"Best ARIMA order: {best_order}")
        print(f"Best seasonal order: {best_seasonal_order}")
        print(f"Best AIC: {best_aic:.2f}")
        
        return best_order, best_seasonal_order
    
    def fit(self, train_data):
        """Fit ARIMA model"""
        print("=== Fitting ARIMA Model ===")
        
        # Extract target variable
        if isinstance(train_data, pd.DataFrame):
            timeseries = train_data['cnt']
        else:
            timeseries = train_data
        
        # Check stationarity
        self.check_stationarity(timeseries, "Training Data")
        
        # Plot decomposition
        self.plot_decomposition(timeseries)
        
        # Plot ACF and PACF
        self.plot_acf_pacf(timeseries)
        
        # Fit ARIMA model
        print(f"Fitting ARIMA{self.order} x {self.seasonal_order} model...")
        
        try:
            self.model = ARIMA(timeseries, order=self.order, seasonal_order=self.seasonal_order)
            self.fitted_model = self.model.fit()
            
            print("Model fitted successfully!")
            print(self.fitted_model.summary())
            
        except Exception as e:
            print(f"Error fitting ARIMA model: {e}")
            print("Trying simpler model...")
            
            # Try simpler model
            simple_order = (1, 1, 1)
            simple_seasonal = (1, 1, 1, 24)
            
            self.model = ARIMA(timeseries, order=simple_order, seasonal_order=simple_seasonal)
            self.fitted_model = self.model.fit()
            
            print("Simpler model fitted successfully!")
            print(self.fitted_model.summary())
    
    def predict(self, steps):
        """Make predictions"""
        if self.fitted_model is None:
            raise ValueError("Model must be fitted before making predictions")
        
        # Make forecast
        forecast = self.fitted_model.forecast(steps=steps)
        
        # Get confidence intervals
        forecast_ci = self.fitted_model.get_forecast(steps=steps).conf_int()
        
        return forecast, forecast_ci
    
    def predict_with_uncertainty(self, steps, confidence_levels=None):
        """Make predictions with multiple confidence levels"""
        if confidence_levels is None:
            confidence_levels = config.CONFIDENCE_LEVELS
        
        predictions = {}
        
        for alpha in confidence_levels:
            forecast_result = self.fitted_model.get_forecast(steps=steps, alpha=1-alpha)
            
            predictions[f'confidence_{int(alpha*100)}'] = {
                'forecast': forecast_result.predicted_mean,
                'lower_bound': forecast_result.conf_int().iloc[:, 0],
                'upper_bound': forecast_result.conf_int().iloc[:, 1]
            }
        
        return predictions
    
    def evaluate(self, test_data):
        """Evaluate model performance"""
        if isinstance(test_data, pd.DataFrame):
            y_true = test_data['cnt'].values
        else:
            y_true = test_data
        
        # Make predictions
        steps = len(y_true)
        y_pred, _ = self.predict(steps)
        
        # Calculate metrics
        metrics = calculate_metrics(y_true, y_pred)
        
        print("=== ARIMA Model Evaluation ===")
        for metric, value in metrics.items():
            print(f"{metric}: {value:.4f}")
        
        return metrics, y_pred
    
    def plot_predictions(self, train_data, test_data, predictions):
        """Plot predictions vs actual values"""
        plt.figure(figsize=(15, 8))
        
        # Extract time series
        if isinstance(train_data, pd.DataFrame):
            train_ts = train_data['cnt']
            test_ts = test_data['cnt']
        else:
            train_ts = train_data
            test_ts = test_data
        
        # Plot training data
        plt.plot(train_ts.index[-100:], train_ts.values[-100:], label='Training Data', color='blue')
        
        # Plot test data
        plt.plot(test_ts.index, test_ts.values, label='Actual', color='green')
        
        # Plot predictions
        plt.plot(test_ts.index, predictions, label='ARIMA Predictions', color='red', linestyle='--')
        
        plt.title('ARIMA Model Predictions vs Actual')
        plt.xlabel('Time')
        plt.ylabel('Demand')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(f'{config.PLOTS_DIR}/arima_predictions.png', dpi=config.DPI, bbox_inches='tight')
        plt.show()
    
    def save_model(self, filename='arima_model.pkl'):
        """Save fitted model"""
        if self.fitted_model is None:
            raise ValueError("No fitted model to save")
        
        filepath = os.path.join(config.MODELS_DIR, filename)
        with open(filepath, 'wb') as f:
            pickle.dump(self.fitted_model, f)
        
        print(f"Model saved to {filepath}")
    
    def load_model(self, filename='arima_model.pkl'):
        """Load fitted model"""
        filepath = os.path.join(config.MODELS_DIR, filename)
        
        if os.path.exists(filepath):
            with open(filepath, 'rb') as f:
                self.fitted_model = pickle.load(f)
            print(f"Model loaded from {filepath}")
        else:
            raise FileNotFoundError(f"Model file not found: {filepath}")
    
    def residual_analysis(self):
        """Perform residual analysis"""
        if self.fitted_model is None:
            raise ValueError("Model must be fitted before residual analysis")
        
        residuals = self.fitted_model.resid
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # Residuals plot
        axes[0, 0].plot(residuals)
        axes[0, 0].set_title('Residuals')
        axes[0, 0].grid(True, alpha=0.3)
        
        # Residuals histogram
        axes[0, 1].hist(residuals, bins=30, edgecolor='black')
        axes[0, 1].set_title('Residuals Distribution')
        
        # Q-Q plot
        from scipy import stats
        stats.probplot(residuals, dist="norm", plot=axes[1, 0])
        axes[1, 0].set_title('Q-Q Plot')
        
        # ACF of residuals
        plot_acf(residuals, lags=40, ax=axes[1, 1])
        axes[1, 1].set_title('ACF of Residuals')
        
        plt.tight_layout()
        plt.savefig(f'{config.PLOTS_DIR}/arima_residual_analysis.png', dpi=config.DPI, bbox_inches='tight')
        plt.show()

if __name__ == "__main__":
    # Example usage
    data_processor = DataProcessor()
    train_data, val_data, test_data = data_processor.split_data()
    
    # Initialize and fit ARIMA model
    arima = ARIMAPredictor()
    arima.fit(train_data)
    
    # Evaluate model
    metrics, predictions = arima.evaluate(test_data)
    
    # Plot predictions
    arima.plot_predictions(train_data, test_data, predictions)
    
    # Residual analysis
    arima.residual_analysis()
    
    # Save model
    arima.save_model()
